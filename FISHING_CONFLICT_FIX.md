# 摸鱼和收入调整数据冲突修复

## 问题描述

在修改某一个日期的时间段数据时，会把摸鱼和收入调整的数据给清空。这个模态框是刚从 `miniprogram/pages/calendar/index.js` 中封装独立的，但是漏了处理摸鱼和收入调整数据的逻辑。

## 问题原因

1. **缺少摸鱼冲突检测**：组件的 `onConfirmSchedule` 方法没有检查摸鱼数据冲突
2. **缺少数据验证**：组件没有验证时间输入的合法性
3. **缺少收入数据处理**：组件没有处理收入数据的小数位数限制
4. **缺少冲突处理逻辑**：组件没有处理摸鱼数据冲突的相关方法
5. **服务层数据保留不完整**：`timeSegmentService.setDaySchedule` 和 `cleanDayDataForStorage` 方法没有保留收入调整数据

## 修复方案

### 1. 修改 `onConfirmSchedule` 方法

**原来的逻辑**：
- 简单检查时间冲突
- 直接调用 `_saveScheduleData` 保存数据
- 没有摸鱼冲突检测

**修复后的逻辑**：
- 检查时间冲突
- 验证时间输入合法性（调用 `_validateTimeInputs`）
- 处理收入数据（调用 `_processIncomeData`）
- 使用 `timeSegmentService.setDaySchedule` 保存并检查摸鱼冲突
- 如果有摸鱼冲突，调用 `_handleFishingConflicts` 处理

### 2. 添加的新方法

#### `_validateTimeInputs(timeInputs)`
从页面的 `validateTimeInputs` 方法移植而来，用于验证：
- 时间是否填写完整
- 时间段持续时间是否合理（1分钟到48小时）
- 收入数据是否合法

#### `_processIncomeData(timeInputs)`
从页面的 `processIncomeData` 方法移植而来，用于：
- 限制收入最多保留2位小数
- 重新计算时薪
- 标记数据更新来源

#### `_handleFishingConflicts(result)`
从页面的 `handleFishingConflicts` 方法移植而来，用于：
- 显示摸鱼冲突信息
- 提供三种处理选项：取消修改、删除冲突记录、手动调整

#### `_deleteConflictingFishingRecords(conflicts)`
从页面的 `deleteConflictingFishingRecords` 方法移植而来，用于：
- 删除所有冲突的摸鱼记录
- 重新保存时间段数据
- 通知页面刷新数据

#### `_showFishingConflictEditor(conflicts)`
简化版的冲突编辑器显示，提示用户在页面中手动调整摸鱼记录。

### 3. 移除的方法

#### `_saveScheduleData(selectedDate)`
移除了原来的简单保存方法，改为直接使用 `timeSegmentService.setDaySchedule`，这样可以：
- 利用服务层的摸鱼冲突检测
- 保持与页面逻辑的一致性
- 确保数据完整性

### 4. 服务层修复

#### 修改 `timeSegmentService.setDaySchedule` 方法
在保存时间段数据时，现在会保留现有的收入调整数据：
- `extraIncome` - 额外收入总额
- `deductions` - 扣款总额
- `netIncome` - 净收入
- `extraIncomeItems` - 额外收入项目列表
- `deductionItems` - 扣款项目列表

#### 修改 `cleanDayDataForStorage` 方法
在清理数据用于存储时，现在会保留收入调整相关的所有字段，确保这些数据不会在保存时丢失。

## 修复效果

1. **摸鱼数据保护**：修改时间段时会检查摸鱼数据冲突，不会意外清空摸鱼记录
2. **收入调整数据保护**：使用正确的服务方法保存，保留现有的收入调整数据
3. **数据验证**：增加了完整的时间输入验证，提高数据质量
4. **用户体验**：提供明确的冲突处理选项，用户可以选择如何处理冲突

## 测试建议

1. **基本功能测试**：
   - 创建一个有时间段的日期
   - 添加一些摸鱼记录
   - 修改时间段，确保摸鱼记录不会被清空

2. **冲突处理测试**：
   - 创建摸鱼记录在某个工作时间段内
   - 修改该工作时间段，使摸鱼记录不再完全包含在内
   - 验证是否显示冲突处理选项

3. **数据验证测试**：
   - 输入无效的时间（如结束时间早于开始时间）
   - 输入过大的收入数值
   - 验证是否显示相应的错误提示

## 相关文件

- `miniprogram/components/schedule-modal/index.js` - 主要修改文件（组件层修复）
- `miniprogram/pages/calendar/index.js` - 参考的原始逻辑
- `miniprogram/core/services/time-segment-service.js` - 时间段服务修改（服务层修复）

## 修改总结

本次修复涉及两个层面：

1. **组件层修复**：在 `schedule-modal` 组件中添加了完整的摸鱼冲突检测和处理逻辑
2. **服务层修复**：在 `time-segment-service` 中修复了收入调整数据保留的问题

这确保了无论是摸鱼数据还是收入调整数据，在修改时间段时都能得到正确的保护和处理。
