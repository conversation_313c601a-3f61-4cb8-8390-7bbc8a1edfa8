/**
 * 反馈意见数据库操作
 */

const BaseDB = require('./base')

class FeedbackDB extends BaseDB {
  constructor() {
    super('feedback')
  }

  /**
   * 创建反馈记录
   * @param {Object} feedbackData - 反馈数据
   * @returns {Promise<Object>} 创建结果
   */
  async createFeedback(feedbackData) {
    const defaultFeedbackData = {
      userId: feedbackData.userId,
      category: feedbackData.category, // 直接使用中文分类
      email: feedbackData.email || '',
      content: feedbackData.content,
      status: 'pending', // 默认状态：待处理
      reply: '',
      replyTime: null,
      isDeleted: false, // 软删除标记
      ...feedbackData
    }

    return await this.create(defaultFeedbackData)
  }

  /**
   * 根据用户ID获取反馈列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbacksByUserId(userId, options = {}) {
    const queryOptions = {
      orderBy: { field: 'createTime', order: 'desc' },
      limit: options.limit || 20,
      skip: options.skip || 0
    }

    // 只查询未删除的反馈
    return await this.find({ 
      userId, 
      isDeleted: false 
    }, queryOptions)
  }

  /**
   * 软删除反馈
   * @param {string} feedbackId - 反馈ID
   * @param {string} userId - 用户ID（确保只能删除自己的反馈）
   * @returns {Promise<Object>} 删除结果
   */
  async softDeleteFeedback(feedbackId, userId) {
    try {
      // 先查询确认是该用户的反馈
      const feedbackResult = await this.findById(feedbackId)
      
      if (!feedbackResult.success) {
        return {
          success: false,
          errMsg: '反馈记录不存在'
        }
      }

      if (feedbackResult.data.userId !== userId) {
        return {
          success: false,
          errMsg: '无权限删除此反馈'
        }
      }

      if (feedbackResult.data.isDeleted) {
        return {
          success: false,
          errMsg: '反馈已被删除'
        }
      }

      // 执行软删除
      const result = await this.update(
        { _id: feedbackId, userId },
        { isDeleted: true }
      )

      return result
    } catch (error) {
      console.error('软删除反馈失败:', error)
      return {
        success: false,
        errMsg: error.message || '删除反馈失败'
      }
    }
  }

  /**
   * 获取用户反馈统计
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 统计结果
   */
  async getFeedbackStats(userId) {
    try {
      // 获取所有未删除的反馈
      const allFeedbackResult = await this.find({ 
        userId, 
        isDeleted: false 
      })

      if (!allFeedbackResult.success) {
        return {
          success: false,
          errMsg: '获取统计数据失败'
        }
      }

      const feedbacks = allFeedbackResult.data
      const stats = {
        total: feedbacks.length,
        pending: feedbacks.filter(f => f.status === 'pending').length,
        replied: feedbacks.filter(f => f.status === 'replied').length,
        closed: feedbacks.filter(f => f.status === 'closed').length
      }

      return {
        success: true,
        data: stats
      }
    } catch (error) {
      console.error('获取反馈统计失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取统计数据失败'
      }
    }
  }

  /**
   * 根据ID获取反馈详情
   * @param {string} feedbackId - 反馈ID
   * @returns {Promise<Object>} 查询结果
   */
  async getFeedbackById(feedbackId) {
    return await this.findById(feedbackId)
  }

  /**
   * 管理员回复反馈（通过数据库直接操作）
   * @param {string} feedbackId - 反馈ID
   * @param {string} reply - 回复内容
   * @returns {Promise<Object>} 更新结果
   */
  async replyFeedback(feedbackId, reply) {
    const updateData = {
      reply: reply,
      replyTime: new Date(),
      status: 'replied'
    }

    return await this.update({ _id: feedbackId }, updateData)
  }

  /**
   * 关闭反馈（管理员操作）
   * @param {string} feedbackId - 反馈ID
   * @returns {Promise<Object>} 更新结果
   */
  async closeFeedback(feedbackId) {
    return await this.update({ _id: feedbackId }, { status: 'closed' })
  }
}

module.exports = new FeedbackDB()
