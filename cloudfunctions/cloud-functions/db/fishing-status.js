/**
 * 摸鱼状态数据库操作
 */

const BaseDB = require('./base')

class FishingStatusDB extends BaseDB {
  constructor() {
    super('fishing_status')
  }

  /**
   * 创建摸鱼状态记录
   * @param {Object} fishingData - 摸鱼数据
   * @returns {Promise<Object>} 创建结果
   */
  async createFishingStatus(fishingData) {
    const now = new Date()
    
    // 计算最大结束时间（工作段结束时间）
    const workEndMinutes = fishingData.workSegment.end
    const today = new Date()
    today.setHours(Math.floor(workEndMinutes / 60), workEndMinutes % 60, 0, 0)
    
    const defaultData = {
      userId: fishingData.userId,
      openid: fishingData.openid,
      workId: fishingData.workId,
      startTime: now.toISOString(),
      startMinutes: fishingData.startMinutes,
      workSegment: fishingData.workSegment,
      maxEndTime: today.toISOString(),
      remark: fishingData.remark || '',
      ...fishingData
    }

    return await this.create(defaultData)
  }

  /**
   * 根据用户ID查找摸鱼状态
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 查询结果
   */
  async findByUserId(userId) {
    return await this.findOne({ userId })
  }

  /**
   * 根据用户ID删除摸鱼状态
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 删除结果
   */
  async deleteByUserId(userId) {
    return await this.delete({ userId })
  }

  /**
   * 获取当前摸鱼人数（带智能过期清理）
   * @returns {Promise<Object>} 统计结果
   */
  async getCurrentFishingCount() {
    try {
      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const currentDateString = now.toDateString()
      
      // 查询所有摸鱼状态
      const allStatusResult = await this.find({})
      
      if (!allStatusResult.success) {
        return allStatusResult
      }

      const allStatus = allStatusResult.data || []
      let activeCount = 0
      const expiredIds = []
      
      for (const status of allStatus) {
        // 检查是否过期的三个条件：
        // 1. 超过工作段结束时间
        // 2. 超过最大结束时间  
        // 3. 跨日期了（简单的日期检查）
        
        const statusDate = new Date(status.startTime)
        const maxEndTime = new Date(status.maxEndTime)
        
        const isExpired = 
          currentMinutes > status.workSegment.end || 
          now > maxEndTime ||
          currentDateString !== statusDate.toDateString()
        
        if (isExpired) {
          expiredIds.push(status._id)
          console.log(`摸鱼状态过期，将清理: userId=${status.userId}, startTime=${status.startTime}`)
        } else {
          activeCount++
        }
      }
      
      // 批量清理过期状态
      if (expiredIds.length > 0) {
        console.log(`清理 ${expiredIds.length} 个过期的摸鱼状态`)
        await this.delete({ _id: this.db.command.in(expiredIds) })
      }
      
      return {
        success: true,
        data: {
          count: activeCount,
          timestamp: now.toISOString(),
          cleanedExpired: expiredIds.length
        }
      }
    } catch (error) {
      console.error('获取当前摸鱼人数失败:', error)
      return {
        success: false,
        errMsg: error.message || '获取当前摸鱼人数失败'
      }
    }
  }

  /**
   * 检查用户是否已有摸鱼状态
   * @param {string} userId - 用户ID
   * @returns {Promise<Object>} 检查结果
   */
  async hasActiveFishingStatus(userId) {
    const result = await this.findByUserId(userId)
    return {
      success: true,
      hasActive: result.success && result.data !== null
    }
  }

  /**
   * 批量删除过期状态（定期清理用）
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupExpiredStatus() {
    try {
      const now = new Date()
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const currentDateString = now.toDateString()
      
      // 查询所有状态
      const allStatusResult = await this.find({})
      if (!allStatusResult.success) {
        return allStatusResult
      }

      const allStatus = allStatusResult.data || []
      const expiredIds = []
      
      for (const status of allStatus) {
        const statusDate = new Date(status.startTime)
        const maxEndTime = new Date(status.maxEndTime)
        
        const isExpired = 
          currentMinutes > status.workSegment.end || 
          now > maxEndTime ||
          currentDateString !== statusDate.toDateString()
        
        if (isExpired) {
          expiredIds.push(status._id)
        }
      }
      
      if (expiredIds.length > 0) {
        await this.delete({ _id: this.db.command.in(expiredIds) })
      }
      
      return {
        success: true,
        data: {
          cleanedCount: expiredIds.length,
          timestamp: now.toISOString()
        }
      }
    } catch (error) {
      console.error('清理过期摸鱼状态失败:', error)
      return {
        success: false,
        errMsg: error.message || '清理过期摸鱼状态失败'
      }
    }
  }
}

module.exports = new FishingStatusDB()
