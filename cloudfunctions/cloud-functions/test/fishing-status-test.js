/**
 * 摸鱼状态功能测试
 * 用于验证摸鱼状态系统的基本功能，特别是 deleteMany 方法
 */

const BaseDB = require('../db/base')

/**
 * 测试 BaseDB 的 deleteMany 方法
 */
async function testDeleteManyMethod() {
  try {
    console.log('开始测试 BaseDB deleteMany 方法...')
    
    // 创建一个测试用的 BaseDB 实例
    const testDB = new BaseDB('test_collection')
    
    // 检查 deleteMany 方法是否存在
    if (typeof testDB.deleteMany === 'function') {
      console.log('✅ deleteMany 方法存在')
      
      // 测试方法调用（不实际执行数据库操作）
      console.log('deleteMany 方法类型:', typeof testDB.deleteMany)
      console.log('deleteMany 方法定义:', testDB.deleteMany.toString().substring(0, 100) + '...')
      
    } else {
      console.log('❌ deleteMany 方法不存在')
      return false
    }
    
    // 检查 delete 方法是否存在（作为对比）
    if (typeof testDB.delete === 'function') {
      console.log('✅ delete 方法存在')
    } else {
      console.log('❌ delete 方法不存在')
    }
    
    console.log('✅ BaseDB deleteMany 方法测试通过')
    return true
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
    return false
  }
}

/**
 * 测试摸鱼状态数据库类
 */
async function testFishingStatusDB() {
  try {
    console.log('开始测试 FishingStatusDB...')
    
    const fishingStatusDB = require('../db/fishing-status')
    
    // 检查 deleteMany 方法是否可用
    if (typeof fishingStatusDB.deleteMany === 'function') {
      console.log('✅ FishingStatusDB 有 deleteMany 方法')
    } else {
      console.log('❌ FishingStatusDB 没有 deleteMany 方法')
      return false
    }
    
    // 检查数据库连接相关属性
    console.log('数据库实例信息:')
    console.log('- collectionName:', fishingStatusDB.collectionName)
    console.log('- db 对象存在:', !!fishingStatusDB.db)
    console.log('- collection 对象存在:', !!fishingStatusDB.collection)
    
    console.log('✅ FishingStatusDB 测试通过')
    return true
    
  } catch (error) {
    console.error('❌ FishingStatusDB 测试失败:', error)
    return false
  }
}

/**
 * 测试具体的 deleteMany 调用场景
 */
async function testDeleteManyScenario() {
  try {
    console.log('开始测试 deleteMany 具体调用场景...')

    const fishingStatusDB = require('../db/fishing-status')

    // 模拟过期ID数组
    const expiredIds = ['test-id-1', 'test-id-2', 'test-id-3']

    // 测试构建查询条件（不实际执行数据库操作）
    const queryCondition = { _id: fishingStatusDB.db.command.in(expiredIds) }
    console.log('查询条件构建成功:', JSON.stringify(queryCondition, null, 2))

    // 验证 deleteMany 方法可以被调用（不实际执行）
    console.log('deleteMany 方法可调用:', typeof fishingStatusDB.deleteMany === 'function')

    console.log('✅ deleteMany 调用场景测试通过')
    return true

  } catch (error) {
    console.error('❌ deleteMany 调用场景测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('=== 开始摸鱼状态功能测试 ===\n')

  const test1 = await testDeleteManyMethod()
  console.log('')

  const test2 = await testFishingStatusDB()
  console.log('')

  const test3 = await testDeleteManyScenario()
  console.log('')

  if (test1 && test2 && test3) {
    console.log('🎉 所有测试通过！deleteMany 方法修复成功')
  } else {
    console.log('❌ 部分测试失败')
  }

  console.log('\n=== 测试完成 ===')
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testDeleteManyMethod,
  testFishingStatusDB,
  testDeleteManyScenario,
  runAllTests
}
