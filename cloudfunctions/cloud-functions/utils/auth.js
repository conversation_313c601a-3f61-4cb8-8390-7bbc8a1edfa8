/**
 * 认证相关工具函数
 */

const cloud = require('wx-server-sdk')
const usersDB = require('../db/users')
const { formatDate } = require('./date.js')

/**
 * 获取当前用户的openid
 * @returns {string} 用户openid
 */
function getCurrentOpenid() {
  const wxContext = cloud.getWXContext()
  return wxContext.OPENID
}

/**
 * 获取当前用户的unionid
 * @returns {string} 用户unionid
 */
function getCurrentUnionid() {
  const wxContext = cloud.getWXContext()
  return wxContext.UNIONID
}

/**
 * 获取当前用户信息
 * @returns {Promise<Object>} 用户信息
 */
async function getCurrentUser() {
  try {
    const openid = getCurrentOpenid()
    if (!openid) {
      return {
        success: false,
        errMsg: '无法获取用户标识'
      }
    }

    const result = await usersDB.findByOpenid(openid)
    if (!result.success) {
      return result
    }

    if (!result.data) {
      return {
        success: false,
        errMsg: '用户不存在',
        needRegister: true
      }
    }

    return {
      success: true,
      data: result.data
    }
  } catch (error) {
    console.error('获取当前用户失败:', error)
    return {
      success: false,
      errMsg: error.message || '获取当前用户失败'
    }
  }
}

/**
 * 检查用户是否为VIP会员
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<boolean>} 是否为VIP会员
 */
async function checkVipMembership(openid = null) {
  try {
    const targetOpenid = openid || getCurrentOpenid()
    if (!targetOpenid) {
      return false
    }

    return await usersDB.isVipMember(targetOpenid)
  } catch (error) {
    console.error('检查VIP会员状态失败:', error)
    return false
  }
}

/**
 * 获取用户VIP状态
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} VIP状态信息
 */
async function getUserVipStatus(openid = null) {
  try {
    const targetOpenid = openid || getCurrentOpenid()
    if (!targetOpenid) {
      return { status: false, expiredAt: null }
    }

    return await usersDB.getVipStatus(targetOpenid)
  } catch (error) {
    console.error('获取VIP状态失败:', error)
    return { status: false, expiredAt: null }
  }
}

/**
 * 验证VIP会员权限
 * @param {string} featureName - 功能名称
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} 验证结果
 */
async function validateVipPermission(featureName = '此功能', openid = null) {
  try {
    const isVipMember = await checkVipMembership(openid)

    if (!isVipMember) {
      return {
        success: false,
        errMsg: `${featureName}仅限VIP会员使用`,
        needUpgrade: true,
        vipRequired: true
      }
    }

    return {
      success: true,
      message: '权限验证通过'
    }
  } catch (error) {
    console.error('验证VIP权限失败:', error)
    return {
      success: false,
      errMsg: error.message || '权限验证失败'
    }
  }
}

/**
 * 确保用户存在，不存在则创建
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<Object>} 操作结果
 */
async function ensureUserExists(userInfo = {}) {
  try {
    const openid = getCurrentOpenid()
    if (!openid) {
      return {
        success: false,
        errMsg: '无法获取用户标识'
      }
    }

    // 检查用户是否已存在
    const existingResult = await usersDB.findByOpenid(openid)
    if (existingResult.success && existingResult.data) {
      return {
        success: true,
        data: existingResult.data,
        isNewUser: false
      }
    }

    // 创建新用户
    const createResult = await usersDB.createUser({
      openid,
      ...userInfo
    })

    if (!createResult.success) {
      return createResult
    }

    return {
      success: true,
      data: createResult.data,
      isNewUser: true
    }
  } catch (error) {
    console.error('确保用户存在失败:', error)
    return {
      success: false,
      errMsg: error.message || '用户操作失败'
    }
  }
}

/**
 * 增加API调用计数并检查是否超限
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @param {string} apiType - API类型
 * @returns {Promise<Object>} 检查结果
 */
async function incrementApiCallCount(openid = null, apiType = 'general') {
  try {
    const targetOpenid = openid || getCurrentOpenid()
    if (!targetOpenid) {
      throw new Error('无法获取用户标识')
    }

    const userResult = await usersDB.findByOpenid(targetOpenid)
    if (!userResult.success || !userResult.data) {
      throw new Error('用户不存在')
    }

    const user = userResult.data
    const isVip = user.vip?.status === true

    // API调用限制
    const limits = {
      free: { daily: 100, monthly: 1000 },
      vip: { daily: 1000, monthly: 10000 }
    }

    const userLimits = isVip ? limits.vip : limits.free
    const today = formatDate(new Date(), 'YYYY-MM-DD')
    const apiCallCount = user.apiCallCount || { daily: 0, monthly: 0, lastResetDate: today }

    // 检查是否需要重置计数
    const lastResetDate = new Date(apiCallCount.lastResetDate)
    const currentDate = new Date()

    let needUpdate = false

    // 检查日计数重置
    if (formatDate(lastResetDate, 'YYYY-MM-DD') !== today) {
      apiCallCount.daily = 0
      needUpdate = true
    }

    // 检查月计数重置
    const lastResetMonth = formatDate(lastResetDate, 'YYYY-MM')
    const currentMonth = formatDate(currentDate, 'YYYY-MM')

    if (lastResetMonth !== currentMonth) {
      apiCallCount.monthly = 0
      needUpdate = true
    }

    // 更新计数器
    apiCallCount.daily += 1
    apiCallCount.monthly += 1
    apiCallCount.lastResetDate = today
    needUpdate = true

    // 检查是否超出限制（在增加计数后检查）
    if (apiCallCount.daily > userLimits.daily) {
      // 即使超限也要更新计数
      if (needUpdate) {
        await usersDB.updateApiCallCount(targetOpenid, apiCallCount)
      }
      throw new Error(`日调用次数已达上限 (${userLimits.daily})`)
    }

    if (apiCallCount.monthly > userLimits.monthly) {
      // 即使超限也要更新计数
      if (needUpdate) {
        await usersDB.updateApiCallCount(targetOpenid, apiCallCount)
      }
      throw new Error(`月调用次数已达上限 (${userLimits.monthly})`)
    }

    // 更新数据库
    if (needUpdate) {
      await usersDB.updateApiCallCount(targetOpenid, apiCallCount)
    }

    return {
      success: true,
      limits: userLimits,
      current: apiCallCount,
      isVip: isVip
    }
  } catch (error) {
    console.error('API调用计数失败:', error)
    throw error
  }
}

module.exports = {
  getCurrentOpenid,
  getCurrentUnionid,
  getCurrentUser,
  checkVipMembership,
  getUserVipStatus,
  validateVipPermission,
  ensureUserExists,
  incrementApiCallCount
}
