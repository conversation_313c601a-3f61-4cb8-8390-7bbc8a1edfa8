// 批量操作模态框组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 操作类型：'import' 或 'copy'
    operation: {
      type: String,
      value: 'copy'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 批量复制步骤：1-选择源日期，2-选择目标日期
    batchStep: 1,
    
    // 日历相关
    batchCalendarYear: new Date().getFullYear(),
    batchCalendarMonth: new Date().getMonth() + 1,
    batchCalendarDays: [],
    
    // 导入模式相关
    templateDates: [],
    selectedTemplate: null,
    
    // 批量复制相关
    selectedSourceDate: null,
    selectedSourceDateObject: null,
    sourceSchedulePreview: null,
    selectedTargetDates: [],
    
    // 复制选项
    copyStatus: true,
    copyIncome: true,
    
    // 快速选择相关
    currentMonthWorkdays: 0,
    isHolidayLoading: false,
    
    // 模态框动画状态
    modalVisible: false,

    // 组件内部数据
    currentDate: '',
    currentWorkId: ''
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 监听属性变化
     */
    _onPropertiesChange() {
      if (this.properties.visible) {
        // 初始化模态框
        this._initializeModal()
      } else {
        this.setData({ modalVisible: false })
      }
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      const now = new Date()

      // 重置状态
      this.setData({
        batchStep: 1,
        batchCalendarYear: now.getFullYear(),
        batchCalendarMonth: now.getMonth() + 1,
        selectedSourceDate: null,
        selectedSourceDateObject: null,
        sourceSchedulePreview: null,
        selectedTargetDates: [],
        selectedTemplate: null,
        copyStatus: true,
        copyIncome: true,
        currentMonthWorkdays: 0,
        isHolidayLoading: false
      }, () => {
        // 根据操作类型初始化
        if (this.properties.operation === 'import') {
          this._initializeImportMode()
        } else {
          this._initializeCopyMode()
        }

        // 延迟显示动画
        setTimeout(() => {
          this.setData({ modalVisible: true })
        }, 50)
      })
    },

    /**
     * 初始化导入模式
     */
    _initializeImportMode() {
      this._loadTemplateDates()
    },

    /**
     * 初始化批量复制模式
     */
    _initializeCopyMode() {
      this._generateBatchCalendar()
    },

    /**
     * 获取时间段服务实例
     */
    _getTimeSegmentService() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.timeSegmentService) {
          return currentPage.timeSegmentService
        }
      }
      return null
    },

    /**
     * 获取节假日管理器
     */
    _getHolidayManager() {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.holidayManager) {
          return currentPage.holidayManager
        }
      }
      return null
    },

    /**
     * 加载模板日期数据
     */
    _loadTemplateDates() {
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      // 获取所有有数据的日期作为模板
      const templateDates = timeSegmentService.getDatesWithData()
        .map(date => {
          const dayData = timeSegmentService.getDayData(date)
          const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
          const statusConfig = timeSegmentService.getDateStatusConfig(status)
          
          return {
            date,
            dateKey: date.toISOString(),
            dateText: this._formatDate(date),
            dailyIncome: dayData.dailyIncome || 0,
            segmentCount: dayData.segments.length,
            statusConfig: {
              ...statusConfig,
              name: statusConfig.name || '无状态'
            }
          }
        })
        .filter(item => item.segmentCount > 0)

      this.setData({
        templateDates
      })
    },

    /**
     * 生成批量操作日历
     */
    _generateBatchCalendar() {
      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      const { batchCalendarYear, batchCalendarMonth } = this.data
      
      // 获取当月第一天和最后一天
      const firstDay = new Date(batchCalendarYear, batchCalendarMonth - 1, 1)
      const lastDay = new Date(batchCalendarYear, batchCalendarMonth, 0)
      
      // 获取当月第一天是星期几（0-6，0是星期日）
      const firstDayOfWeek = firstDay.getDay()
      
      // 获取当月天数
      const daysInMonth = lastDay.getDate()
      
      // 生成日历数据
      const calendarDays = []
      let workdaysCount = 0
      
      // 添加上个月的空白天数
      for (let i = 0; i < firstDayOfWeek; i++) {
        calendarDays.push(null)
      }
      
      // 添加当月的天数
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(batchCalendarYear, batchCalendarMonth - 1, day)
        const dayData = timeSegmentService.getDayData(date)
        const hasData = dayData.segments.length > 0
        const status = timeSegmentService.getDateStatus(date, this.data.currentWorkId)
        const statusConfig = timeSegmentService.getDateStatusConfig(status)
        
        // 检查是否在任职日期范围内
        const isInEmploymentRange = this._isDateInEmploymentRange(date)
        
        // 检查是否是工作日
        const isWorkday = this._isWorkday(date)
        if (isWorkday && isInEmploymentRange) {
          workdaysCount++
        }
        
        // 检查是否被选中
        const isSelectedSource = this._isSelectedSource(date)
        const isSelectedTarget = this._isSelectedTarget(date)
        
        calendarDays.push({
          day,
          date,
          hasData,
          isCurrentMonth: true,
          status,
          statusConfig,
          dailyIncome: dayData.dailyIncome || 0,
          segmentCount: dayData.segments.length,
          isInEmploymentRange,
          isWorkday,
          isSelectedSource,
          isSelectedTarget
        })
      }
      
      this.setData({
        batchCalendarDays: calendarDays,
        currentMonthWorkdays: workdaysCount
      })
    },

    /**
     * 检查日期是否在任职范围内
     */
    _isDateInEmploymentRange(date) {
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (currentPage.isDateInEmploymentRange) {
          return currentPage.isDateInEmploymentRange(date)
        }
      }
      return true
    },

    /**
     * 检查是否是工作日
     */
    _isWorkday(date) {
      const holidayManager = this._getHolidayManager()
      if (holidayManager) {
        return holidayManager.isWorkday(date)
      }
      
      // 如果没有节假日管理器，简单判断周一到周五为工作日
      const dayOfWeek = date.getDay()
      return dayOfWeek >= 1 && dayOfWeek <= 5
    },

    /**
     * 检查日期是否被选为源日期
     */
    _isSelectedSource(date) {
      if (!this.data.selectedSourceDateObject) return false
      return this._isSameDate(date, this.data.selectedSourceDateObject)
    },

    /**
     * 检查日期是否被选为目标日期
     */
    _isSelectedTarget(date) {
      return this.data.selectedTargetDates.some(targetDate => 
        this._isSameDate(date, targetDate)
      )
    },

    /**
     * 检查两个日期是否相同
     */
    _isSameDate(date1, date2) {
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate()
    },

    /**
     * 格式化日期
     */
    _formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    /**
     * 上一年
     */
    onBatchPreviousYear() {
      this.setData({
        batchCalendarYear: this.data.batchCalendarYear - 1
      })
      this._generateBatchCalendar()
    },

    /**
     * 下一年
     */
    onBatchNextYear() {
      this.setData({
        batchCalendarYear: this.data.batchCalendarYear + 1
      })
      this._generateBatchCalendar()
    },

    /**
     * 上一月
     */
    onBatchPreviousMonth() {
      let { batchCalendarYear, batchCalendarMonth } = this.data

      batchCalendarMonth--
      if (batchCalendarMonth < 1) {
        batchCalendarMonth = 12
        batchCalendarYear--
      }

      this.setData({
        batchCalendarYear,
        batchCalendarMonth
      })

      this._generateBatchCalendar()
    },

    /**
     * 下一月
     */
    onBatchNextMonth() {
      let { batchCalendarYear, batchCalendarMonth } = this.data

      batchCalendarMonth++
      if (batchCalendarMonth > 12) {
        batchCalendarMonth = 1
        batchCalendarYear++
      }

      this.setData({
        batchCalendarYear,
        batchCalendarMonth
      })

      this._generateBatchCalendar()
    },

    /**
     * 日期点击处理
     */
    onBatchDateTap(e) {
      const { index } = e.currentTarget.dataset
      const dayItem = this.data.batchCalendarDays[index]

      console.log('日期点击:', { index, dayItem, batchStep: this.data.batchStep })

      if (!dayItem || !dayItem.isCurrentMonth) {
        console.log('无效日期点击')
        return
      }

      if (this.data.batchStep === 1) {
        console.log('步骤1：选择源日期')
        // 步骤1：选择源日期
        this._handleSourceDateSelection(dayItem)
      } else {
        console.log('步骤2：选择目标日期')
        // 步骤2：选择目标日期
        this._handleTargetDateSelection(dayItem)
      }
    },

    /**
     * 处理源日期选择
     */
    _handleSourceDateSelection(dayItem) {
      console.log('处理源日期选择:', dayItem)

      if (!dayItem.hasData) {
        wx.showToast({
          title: '该日期没有工作安排',
          icon: 'none'
        })
        return
      }

      const timeSegmentService = this._getTimeSegmentService()
      if (!timeSegmentService) {
        console.warn('timeSegmentService not available')
        return
      }

      // 获取工作安排详情
      const dayData = timeSegmentService.getDayData(dayItem.date)
      const formattedSegments = dayData.segments.map(segment => ({
        ...segment,
        startTime: this._formatTime(segment.start),
        endTime: this._formatTime(segment.end),
        typeText: this._getTypeText(segment.type),
        duration: this._formatDuration(segment.end - segment.start)
      }))

      const schedulePreview = {
        date: this._formatDate(dayItem.date),
        dailyIncome: dayData.dailyIncome || 0,
        segments: formattedSegments
      }

      console.log('设置源日期数据:', {
        selectedSourceDate: this._formatDate(dayItem.date),
        schedulePreview
      })

      this.setData({
        selectedSourceDate: this._formatDate(dayItem.date),
        selectedSourceDateObject: dayItem.date,
        sourceSchedulePreview: schedulePreview
      }, () => {
        console.log('源日期设置完成，当前状态:', {
          selectedSourceDate: this.data.selectedSourceDate,
          batchStep: this.data.batchStep
        })
      })

      // 重新生成日历以更新选中状态
      this._generateBatchCalendar()
    },

    /**
     * 处理目标日期选择
     */
    _handleTargetDateSelection(dayItem) {
      if (!dayItem.isInEmploymentRange) {
        wx.showToast({
          title: '该日期不在任职范围内',
          icon: 'none'
        })
        return
      }

      const selectedTargetDates = [...this.data.selectedTargetDates]
      const dateIndex = selectedTargetDates.findIndex(date =>
        this._isSameDate(date, dayItem.date)
      )

      if (dateIndex >= 0) {
        // 已选中，取消选择
        selectedTargetDates.splice(dateIndex, 1)
      } else {
        // 未选中，添加选择
        selectedTargetDates.push(dayItem.date)
      }

      this.setData({
        selectedTargetDates
      })

      // 重新生成日历以更新选中状态
      this._generateBatchCalendar()
    },

    /**
     * 格式化时间（分钟转时间显示）
     */
    _formatTime(minutes) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`
    },

    /**
     * 获取类型文本
     */
    _getTypeText(type) {
      const typeMap = {
        'work': '工作',
        'rest': '休息',
        'overtime': '加班'
      }
      return typeMap[type] || type
    },

    /**
     * 格式化持续时间
     */
    _formatDuration(minutes) {
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60

      if (hours > 0 && mins > 0) {
        return `${hours}小时${mins}分钟`
      } else if (hours > 0) {
        return `${hours}小时`
      } else {
        return `${mins}分钟`
      }
    },

    /**
     * 模板选择（导入模式）
     */
    onSelectTemplate(e) {
      const { index } = e.currentTarget.dataset
      const template = this.data.templateDates[index]

      this.setData({
        selectedTemplate: template
      })
    },

    /**
     * 确认源日期选择
     */
    onConfirmSourceDate() {
      if (!this.data.selectedSourceDate) {
        wx.showToast({
          title: '请先选择源日期',
          icon: 'none'
        })
        return
      }

      this.setData({
        batchStep: 2
      })

      // 重新生成日历以显示目标日期选择界面
      this._generateBatchCalendar()
    },

    /**
     * 返回源日期选择
     */
    onBackToSourceSelection() {
      this.setData({
        batchStep: 1,
        selectedTargetDates: []
      })

      // 重新生成日历
      this._generateBatchCalendar()
    },

    /**
     * 复制状态选项变化
     */
    onCopyStatusChange(e) {
      this.setData({
        copyStatus: e.detail.value
      })
    },

    /**
     * 复制收入选项变化
     */
    onCopyIncomeChange(e) {
      this.setData({
        copyIncome: e.detail.value
      })
    },

    /**
     * 选择全部工作日
     */
    onSelectAllWorkdays() {
      const selectedTargetDates = []

      this.data.batchCalendarDays.forEach(dayItem => {
        if (dayItem && dayItem.isCurrentMonth && dayItem.isWorkday &&
            dayItem.isInEmploymentRange && !this._isSelectedSource(dayItem.date)) {
          selectedTargetDates.push(dayItem.date)
        }
      })

      this.setData({
        selectedTargetDates
      })

      // 重新生成日历以更新选中状态
      this._generateBatchCalendar()

      wx.showToast({
        title: `已选择${selectedTargetDates.length}个工作日`,
        icon: 'success'
      })
    },

    /**
     * 清空选择
     */
    onClearSelection() {
      this.setData({
        selectedTargetDates: []
      })

      // 重新生成日历以更新选中状态
      this._generateBatchCalendar()

      wx.showToast({
        title: '已清空选择',
        icon: 'success'
      })
    },

    /**
     * 确认批量操作
     */
    onConfirmBatchOperation() {
      if (this.properties.operation === 'import') {
        this._confirmImport()
      } else {
        this._confirmBatchCopy()
      }
    },

    /**
     * 确认导入
     */
    _confirmImport() {
      if (!this.data.selectedTemplate) {
        wx.showToast({
          title: '请先选择要导入的模板',
          icon: 'none'
        })
        return
      }

      try {
        const timeSegmentService = getApp().getTimeSegmentService()
        const sourceDate = this.data.selectedTemplate.date
        const targetDate = new Date(this.data.currentDate)
        const scheduleData = timeSegmentService.getDayData(sourceDate)

        if (!scheduleData || scheduleData.segments.length === 0) {
          wx.showToast({
            title: '源日期没有数据',
            icon: 'none'
          })
          return
        }

        // 组件自己执行导入操作
        timeSegmentService.saveDayData(targetDate, scheduleData, this.data.currentWorkId)

        wx.showToast({
          title: '导入成功',
          icon: 'success'
        })

        // 通知页面数据已更新
        this.triggerEvent('dataUpdated', {
          operation: 'import',
          targetDate: targetDate.toISOString()
        })

        this.onClose()
      } catch (error) {
        console.error('批量操作组件：导入失败', error)
        wx.showToast({
          title: '导入失败',
          icon: 'none'
        })
      }
    },

    /**
     * 确认批量复制
     */
    _confirmBatchCopy() {
      if (!this.data.selectedSourceDate) {
        wx.showToast({
          title: '请先选择源日期',
          icon: 'none'
        })
        return
      }

      if (this.data.selectedTargetDates.length === 0) {
        wx.showToast({
          title: '请至少选择一个目标日期',
          icon: 'none'
        })
        return
      }

      const sourceDate = this.data.selectedSourceDateObject
      const targetDates = [...this.data.selectedTargetDates]
      const options = {
        copyStatus: this.data.copyStatus,
        copyIncome: this.data.copyIncome
      }

      console.log('批量操作组件：确认批量复制', {
        sourceDate,
        targetDates,
        targetDatesLength: targetDates.length,
        options
      })

      try {
        // 组件自己执行批量复制操作
        this._executeBatchCopy(sourceDate, targetDates, options)

        wx.showToast({
          title: `成功复制到${targetDates.length}个日期`,
          icon: 'success'
        })

        // 通知页面数据已更新
        this.triggerEvent('dataUpdated', {
          operation: 'copy',
          sourceDate: sourceDate.toISOString(),
          targetDates: targetDates.map(date => date.toISOString()),
          count: targetDates.length
        })

        this.onClose()
      } catch (error) {
        console.error('批量操作组件：批量复制失败', error)
        wx.showToast({
          title: '批量复制失败',
          icon: 'none'
        })
      }
    },

    /**
     * 取消
     */
    onCancel() {
      this.triggerEvent('cancel')
      this.onClose()
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画
      this.setData({ modalVisible: false })

      // 等待动画完成后触发关闭事件
      setTimeout(() => {
        this.triggerEvent('close')
      }, 300) // 与CSS动画时长一致
    },

    /**
     * 执行批量复制
     */
    _executeBatchCopy(sourceDate, targetDates, options) {
      const timeSegmentService = getApp().getTimeSegmentService()

      // 获取源日期的数据
      const sourceData = timeSegmentService.getDayData(sourceDate)
      if (!sourceData || sourceData.segments.length === 0) {
        throw new Error('源日期没有数据')
      }

      // 批量复制到目标日期
      targetDates.forEach(targetDate => {
        const targetData = { ...sourceData }

        // 根据选项决定是否复制状态和收入
        if (!options.copyStatus) {
          delete targetData.status
        }
        if (!options.copyIncome) {
          targetData.segments = targetData.segments.map(segment => ({
            ...segment,
            income: 0
          }))
          targetData.dailyIncome = 0
        }

        // 保存到目标日期
        timeSegmentService.saveDayData(targetDate, targetData, this.data.currentWorkId)
      })
    },

    /**
     * 阻止事件冒泡
     */
    onStopPropagation() {
      // 阻止点击模态框内容时关闭模态框
    },

    /**
     * 加载组件数据
     */
    _loadComponentData() {
      try {
        // 获取当前工作ID
        const currentWork = getApp().getDataManager().getCurrentWork()
        const currentWorkId = currentWork ? currentWork.id : ''

        // 获取当前日期
        const now = new Date()
        const currentDate = this._formatDateText(now)

        this.setData({
          currentWorkId,
          currentDate
        })

        console.log('批量操作组件：数据加载完成', {
          currentWorkId,
          currentDate
        })
      } catch (error) {
        console.warn('批量操作组件：数据加载失败', error)
      }
    },

    /**
     * 格式化日期文本
     */
    _formatDateText(date) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return `${year}年${month}月${day}日`
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('批量操作组件已加载')
    }
  },

  /**
   * 组件观察器
   */
  observers: {
    'visible': function(visible) {
      this._onPropertiesChange()
    }
  }
})
