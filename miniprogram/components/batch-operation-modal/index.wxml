<!-- 批量操作模态框 -->
<view class="modal {{modalVisible ? 'show' : ''}}" wx:if="{{visible}}" bind:tap="onClose">
  <view class="modal-content batch-modal-content" catch:tap="onStopPropagation">
    <view class="modal-header">
      <view class="modal-title">
        <text wx:if="{{operation === 'import'}}">导入工作安排</text>
        <text wx:elif="{{operation === 'copy' && batchStep === 1}}">选择源日期</text>
        <text wx:elif="{{operation === 'copy' && batchStep === 2}}">选择目标日期</text>
      </view>
      <view class="modal-close" bind:tap="onClose">×</view>
    </view>

    <view class="modal-body">
      <!-- 导入模式 -->
      <view wx:if="{{operation === 'import'}}" class="import-mode">
        <view class="import-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">选择一个已有的工作安排作为模板导入</text>
        </view>

        <view wx:if="{{templateDates.length > 0}}" class="template-list">
          <view class="template-item {{selectedTemplate && selectedTemplate.dateKey === item.dateKey ? 'selected' : ''}}"
                wx:for="{{templateDates}}"
                wx:key="dateKey"
                bind:tap="onSelectTemplate"
                data-index="{{index}}">
            <view class="template-date">
              <text class="date-text">{{item.dateText}}</text>
              <view class="template-status">
                <text class="status-emoji">{{item.statusConfig.icon}}</text>
                <text class="status-text">{{item.statusConfig.name}}</text>
              </view>
            </view>
            <view class="template-info">
              <text class="segment-count">{{item.segmentCount}}个时间段</text>
              <text wx:if="{{item.dailyIncome > 0}}" class="daily-income">¥{{item.dailyIncome}}</text>
            </view>
          </view>
        </view>

        <view wx:else class="empty-templates">
          <view class="empty-icon">📅</view>
          <view class="empty-text">暂无可导入的工作安排</view>
          <view class="empty-tip">请先在其他日期设置工作安排</view>
        </view>
      </view>

      <!-- 批量复制模式 -->
      <view wx:elif="{{operation === 'copy'}}" class="copy-mode">
        <!-- 步骤1：选择源日期 -->
        <view wx:if="{{batchStep === 1}}" class="source-selection">
          <view class="step-tip">
            <text class="tip-icon">📋</text>
            <text class="tip-text">选择要复制的源日期（需要有工作安排）</text>
          </view>

          <!-- 日历 -->
          <view class="batch-calendar">
            <view class="batch-calendar-header">
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                <text class="nav-arrow">‹‹</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                <text class="nav-arrow">‹</text>
              </view>
              <view class="batch-calendar-title">
                <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                <text class="nav-arrow">›</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                <text class="nav-arrow">››</text>
              </view>
            </view>

            <view class="batch-calendar-weekdays">
              <view class="weekday">日</view>
              <view class="weekday">一</view>
              <view class="weekday">二</view>
              <view class="weekday">三</view>
              <view class="weekday">四</view>
              <view class="weekday">五</view>
              <view class="weekday">六</view>
            </view>

            <view class="batch-calendar-days">
              <block wx:for="{{batchCalendarDays}}" wx:key="index">
                <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedSource ? 'selected-source' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}}"
                      bind:tap="onBatchDateTap"
                      data-index="{{index}}"
                      wx:if="{{item}}">
                  <text class="day-number">{{item.day}}</text>
                  <view wx:if="{{item.hasData}}" class="day-indicator" style="background-color: {{item.statusConfig.backgroundColor}};">
                    <text class="indicator-text">{{item.segmentCount}}</text>
                  </view>
                </view>
                <view class="batch-calendar-day empty" wx:else></view>
              </block>
            </view>
          </view>

          <!-- 源日期预览 -->
          <view wx:if="{{sourceSchedulePreview}}" class="source-preview">
            <view class="preview-header">
              <text class="preview-title">{{sourceSchedulePreview.date}} 工作安排</text>
              <text wx:if="{{sourceSchedulePreview.dailyIncome > 0}}" class="preview-income">¥{{sourceSchedulePreview.dailyIncome}}</text>
            </view>
            <view class="preview-segments">
              <view class="segment-item" wx:for="{{sourceSchedulePreview.segments}}" wx:key="index">
                <text class="segment-type">{{item.typeText}}</text>
                <text class="segment-time">{{item.startTime}} - {{item.endTime}}</text>
                <text class="segment-duration">{{item.duration}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 步骤2：选择目标日期 -->
        <view wx:elif="{{batchStep === 2}}" class="target-selection">
          <view class="step-tip">
            <text class="tip-icon">🎯</text>
            <text class="tip-text">选择要复制到的目标日期（可多选）</text>
          </view>

          <!-- 复制选项 -->
          <view class="copy-options">
            <view class="option-group">
              <label class="option-item">
                <checkbox checked="{{copyStatus}}" bind:change="onCopyStatusChange"/>
                <text class="option-text">复制状态</text>
              </label>
              <label class="option-item">
                <checkbox checked="{{copyIncome}}" bind:change="onCopyIncomeChange"/>
                <text class="option-text">复制收入</text>
              </label>
            </view>
          </view>

          <!-- 快速选择 -->
          <view class="quick-actions">
            <view class="quick-btn" bind:tap="onSelectAllWorkdays">
              <text class="quick-icon">📅</text>
              <text class="quick-text">选择全部工作日 ({{currentMonthWorkdays}}天)</text>
            </view>
            <view class="quick-btn" bind:tap="onClearSelection" wx:if="{{selectedTargetDates.length > 0}}">
              <text class="quick-icon">🗑️</text>
              <text class="quick-text">清空选择</text>
            </view>
          </view>

          <!-- 日历 -->
          <view class="batch-calendar">
            <view class="batch-calendar-header">
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousYear">
                <text class="nav-arrow">‹‹</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchPreviousMonth">
                <text class="nav-arrow">‹</text>
              </view>
              <view class="batch-calendar-title">
                <text>{{batchCalendarYear}}年{{batchCalendarMonth}}月</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextMonth">
                <text class="nav-arrow">›</text>
              </view>
              <view class="batch-calendar-nav" bind:tap="onBatchNextYear">
                <text class="nav-arrow">››</text>
              </view>
            </view>

            <view class="batch-calendar-weekdays">
              <view class="weekday">日</view>
              <view class="weekday">一</view>
              <view class="weekday">二</view>
              <view class="weekday">三</view>
              <view class="weekday">四</view>
              <view class="weekday">五</view>
              <view class="weekday">六</view>
            </view>

            <view class="batch-calendar-days">
              <block wx:for="{{batchCalendarDays}}" wx:key="index">
                <view class="batch-calendar-day {{item ? 'current-month' : 'empty'}} {{item && item.hasData ? 'has-data' : ''}} {{item && item.isSelectedSource ? 'selected-source' : ''}} {{item && item.isSelectedTarget ? 'selected-target' : ''}} {{item && item.isWorkday ? 'workday' : ''}} {{item && !item.isInEmploymentRange ? 'day-outside-employment' : ''}}"
                      bind:tap="onBatchDateTap"
                      data-index="{{index}}"
                      wx:if="{{item}}">
                  <text class="day-number">{{item.day}}</text>
                  <view wx:if="{{item.hasData}}" class="day-indicator" style="background-color: {{item.statusConfig.backgroundColor}};">
                    <text class="indicator-text">{{item.segmentCount}}</text>
                  </view>
                </view>
                <view class="batch-calendar-day empty" wx:else></view>
              </block>
            </view>
          </view>

          <!-- 选择统计 -->
          <view wx:if="{{selectedTargetDates.length > 0}}" class="selection-summary">
            <text class="summary-text">已选择 {{selectedTargetDates.length}} 个日期</text>
          </view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <!-- 导入模式底部 -->
      <view wx:if="{{operation === 'import'}}" class="import-footer">
        <view class="btn-secondary modal-btn" bind:tap="onCancel">
          <text>取消</text>
        </view>
        <view class="btn-primary modal-btn {{selectedTemplate ? '' : 'disabled'}}" bind:tap="onConfirmBatchOperation">
          <text>确定导入</text>
        </view>
      </view>

      <!-- 批量复制模式底部 -->
      <view wx:elif="{{operation === 'copy'}}" class="copy-footer">
        <!-- 步骤1底部 -->
        <view wx:if="{{batchStep === 1}}" class="step1-footer">
          <view class="btn-secondary modal-btn" bind:tap="onCancel">
            <text>取消</text>
          </view>
          <view class="btn-primary modal-btn {{selectedSourceDate ? '' : 'disabled'}}" bind:tap="onConfirmSourceDate">
            <text>下一步</text>
          </view>
        </view>

        <!-- 步骤2底部 -->
        <view wx:elif="{{batchStep === 2}}" class="step2-footer">
          <view class="btn-secondary modal-btn" bind:tap="onBackToSourceSelection">
            <text>上一步</text>
          </view>
          <view class="btn-primary modal-btn {{selectedTargetDates.length > 0 ? '' : 'disabled'}}" bind:tap="onConfirmBatchOperation">
            <text>确定复制</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
