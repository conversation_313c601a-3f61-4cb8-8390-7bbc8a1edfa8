/* 批量操作模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal.show {
  opacity: 1;
}

.modal-content {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 85%;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.modal.show .modal-content {
  transform: scale(1);
}

.batch-modal-content {
  max-height: 90vh;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background-color: #f5f5f5;
  color: #666;
}

/* 模态框主体 */
.modal-body {
  padding: 20px;
  max-height: 65vh;
  overflow-y: auto;
}

/* 提示信息 */
.import-tip,
.step-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #007aff;
}

.tip-icon {
  font-size: 16px;
}

.tip-text {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 导入模式样式 */
.template-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fff;
}

.template-item:hover {
  border-color: #007aff;
  background-color: #f8f9ff;
}

.template-item.selected {
  border-color: #007aff;
  background-color: #e3f2fd;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
}

.template-date {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.date-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.template-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-emoji {
  font-size: 14px;
}

.status-text {
  font-size: 12px;
  color: #666;
}

.template-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.segment-count {
  font-size: 14px;
  color: #666;
}

.daily-income {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

/* 空状态 */
.empty-templates {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-tip {
  font-size: 14px;
  color: #999;
}

/* 批量日历样式 */
.batch-calendar {
  margin-bottom: 20px;
}

.batch-calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 8px;
}

.batch-calendar-nav {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}

.batch-calendar-nav:hover {
  background-color: #e9ecef;
}

.nav-arrow {
  font-size: 18px;
  color: #666;
  font-weight: bold;
}

.batch-calendar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.batch-calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  padding: 8px 0;
}

.batch-calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.batch-calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.batch-calendar-day.empty {
  cursor: default;
}

.batch-calendar-day.current-month {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.batch-calendar-day.current-month:hover {
  background-color: #e9ecef;
}

.batch-calendar-day.has-data {
  background-color: #e3f2fd;
  border-color: #2196f3;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
}

.batch-calendar-day.has-data:hover {
  background-color: #bbdefb;
}

.batch-calendar-day.selected-source {
  background-color: #ff9800 !important;
  border-color: #ff9800 !important;
  color: white;
  box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
}

.batch-calendar-day.selected-target {
  background-color: #4caf50 !important;
  border-color: #4caf50 !important;
  color: white;
  box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.batch-calendar-day.workday {
  border-color: #2196f3;
}

.batch-calendar-day.day-outside-employment {
  opacity: 0.5;
  cursor: not-allowed;
}

.day-number {
  font-size: 14px;
  font-weight: 500;
  z-index: 1;
}

.batch-calendar-day.selected-source .day-number,
.batch-calendar-day.selected-target .day-number {
  color: white;
}

.day-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: bold;
}

.indicator-text {
  font-size: 10px;
  color: white;
}

/* 源日期预览 */
.source-preview {
  margin-top: 20px;
  padding: 16px;
  background-color: #fff3e0;
  border-radius: 12px;
  border: 1px solid #ffb74d;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ffcc02;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: #e65100;
}

.preview-income {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

.preview-segments {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #ffcc02;
}

.segment-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}

.segment-time {
  font-size: 14px;
  color: #333;
  flex: 1;
}

.segment-duration {
  font-size: 12px;
  color: #666;
}

/* 复制选项 */
.copy-options {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.option-group {
  display: flex;
  gap: 20px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.option-text {
  font-size: 14px;
  color: #333;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.quick-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-btn:hover {
  background-color: #e9ecef;
  border-color: #007aff;
}

.quick-icon {
  font-size: 14px;
}

.quick-text {
  font-size: 14px;
  color: #333;
}

/* 选择统计 */
.selection-summary {
  margin-top: 16px;
  padding: 12px;
  background-color: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #4caf50;
  text-align: center;
}

.summary-text {
  font-size: 14px;
  color: #2e7d32;
  font-weight: 500;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.modal-btn {
  flex: 1;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #666;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background-color: #e9ecef;
  color: #333;
}

.btn-primary {
  background-color: #007aff;
  color: white;
  border: 1px solid #007aff;
}

.btn-primary:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

.btn-primary.disabled {
  background-color: #e9ecef;
  color: #999;
  border-color: #e9ecef;
  cursor: not-allowed;
}

.btn-primary.disabled:hover {
  background-color: #e9ecef;
  color: #999;
  border-color: #e9ecef;
}
