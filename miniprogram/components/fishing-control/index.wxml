<!--摸鱼控制组件-->
<view class="fishing-control {{type}}" wx:if="{{show}}">
  <!-- 摸鱼状态显示 -->
  <view class="fishing-status" wx:if="{{isFishing}}">
    <view class="status-header">
      <text class="status-icon">🐟</text>
      <text class="status-text">摸鱼中</text>
      <view class="edit-remark-btn" bind:tap="onEditRemark">
        <text class="edit-icon">✏️</text>
      </view>
    </view>
    <view class="fishing-duration">{{fishingDuration}}</view>
    <view class="fishing-remark" wx:if="{{fishingState.remark}}">
      <text class="remark-label">备注：</text>
      <text class="remark-text">{{fishingState.remark}}</text>
    </view>
  </view>

  <!-- 摸鱼控制按钮 -->
  <view class="fishing-controls">
    <!-- 开始摸鱼按钮 -->
    <button 
      class="control-btn start-btn" 
      wx:if="{{!isFishing}}"
      disabled="{{loading}}"
      bindtap="onStartFishing"
    >
      <text class="btn-icon">🐟</text>
      <text class="btn-text">开始摸鱼</text>
    </button>

    <!-- 结束摸鱼按钮 -->
    <button 
      class="control-btn end-btn" 
      wx:if="{{isFishing}}"
      disabled="{{loading}}"
      bindtap="onEndFishing"
    >
      <text class="btn-icon">⏹</text>
      <text class="btn-text">结束摸鱼</text>
    </button>
  </view>

  <!-- 备注输入弹窗 -->
  <view class="remark-modal" wx:if="{{showRemarkInput}}">
    <view class="modal-mask" bindtap="onCancelStartFishing"></view>
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">开始摸鱼</text>
      </view>
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">备注（可选）</text>
          <textarea 
            class="remark-input"
            placeholder="描述一下摸鱼时在做什么..."
            value="{{remark}}"
            maxlength="200"
            bindinput="onRemarkInput"
          ></textarea>
          <view class="input-tip">最多200个字符</view>
        </view>
      </view>
      <view class="modal-footer">
        <button 
          class="modal-btn cancel-btn" 
          bindtap="onCancelStartFishing"
        >
          取消
        </button>
        <button 
          class="modal-btn confirm-btn" 
          disabled="{{loading}}"
          bindtap="onConfirmStartFishing"
        >
          开始
        </button>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-overlay" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
  </view>

  <!-- 备注编辑器 -->
  <fishing-remark-editor
    visible="{{showRemarkEditor}}"
    current-remark="{{fishingState.remark}}"
    bind:save="onRemarkEditorSave"
    bind:cancel="onRemarkEditorClose"
    bind:close="onRemarkEditorClose">
  </fishing-remark-editor>
</view>
