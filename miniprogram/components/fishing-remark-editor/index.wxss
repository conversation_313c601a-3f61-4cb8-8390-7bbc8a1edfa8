/* 摸鱼备注编辑器样式 */
@import "../../styles/modal-animations.wxss";

.fishing-remark-editor {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  padding: 20rpx;
  box-sizing: border-box;
}

.fishing-remark-editor.show {
  opacity: 1;
  visibility: visible;
}

.editor-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  width: 90%;
  max-width: 680rpx;
  max-height: 80vh;
  margin: 20rpx 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.fishing-remark-editor.show .editor-content {
  transform: scale(1);
}

/* 标题栏 */
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.editor-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: #e6e6e6;
  transform: scale(0.95);
}

.close-icon {
  font-size: 24rpx;
  color: #666;
}

/* 编辑器主体 */
.editor-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.title-icon {
  font-size: 28rpx;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 快捷备注 */
.quick-remarks-section {
  margin-bottom: 32rpx;
}

.quick-remarks-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.quick-remark-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}

.quick-remark-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.remark-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  margin-bottom: 4rpx;
}

.remark-count {
  font-size: 20rpx;
  color: #666;
}

/* 无快捷备注提示 */
.no-quick-remarks {
  margin-bottom: 32rpx;
}

.no-remarks-tip {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
}

.tip-icon {
  font-size: 28rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

/* 自定义备注输入 */
.custom-remark-section {
  margin-bottom: 16rpx;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  box-sizing: border-box;
  resize: none;
}

.remark-textarea:focus {
  border-color: #1890ff;
  outline: none;
}

.remark-textarea::placeholder {
  color: #bfbfbf;
}

.char-count {
  text-align: right;
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 操作按钮 */
.editor-footer {
  display: flex;
  padding: 24rpx 32rpx 32rpx;
  gap: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.editor-btn {
  flex: 1;
  padding: 16rpx;
  border-radius: 8rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active {
  background: #e6e6e6;
}

.save-btn {
  background: #1890ff;
  color: #fff;
}

.save-btn:not(:disabled):active {
  background: #096dd9;
}

.save-btn:disabled {
  background: #d9d9d9;
  color: #999;
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16rpx;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .editor-content {
    width: 95vw;
  }
  
  .editor-body {
    padding: 24rpx;
  }
  
  .editor-footer {
    padding: 16rpx 24rpx 24rpx;
  }
  
  .quick-remark-item {
    min-width: 100rpx;
    padding: 12rpx 16rpx;
  }
}
