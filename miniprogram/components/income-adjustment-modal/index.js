/**
 * 收入调整模态框组件
 * 用于添加额外收入和扣款
 */

// 引入工具
const { formatDateKey } = require('../../utils/helpers/time-utils.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示模态框
    visible: {
      type: Boolean,
      value: false
    },
    // 模式：'income' 或 'deduction'
    mode: {
      type: String,
      value: 'income'
    },
    // 日期字符串 (YYYY-MM-DD 格式)
    dateString: {
      type: String,
      value: ''
    },
    // 编辑项目ID（如果是编辑模式）
    editItemId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      type: '',
      amount: '',
      description: ''
    },

    // 错误信息
    errors: {},

    // 常见类型
    commonTypes: [],

    // 加载状态
    loading: false,

    // 编辑状态管理
    isEdit: false,
    editItem: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 安全地获取日期对象
     * @param {any} dateValue - 日期值
     * @returns {Date} 有效的日期对象
     */
    /**
     * 将日期字符串转换为Date对象
     * @param {string} dateString - YYYY-MM-DD 格式的日期字符串
     * @returns {Date} Date对象
     */
    parseDate(dateString) {
      if (!dateString || typeof dateString !== 'string') {
        throw new Error('日期字符串无效')
      }

      // 确保是 YYYY-MM-DD 格式
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/
      if (!dateRegex.test(dateString)) {
        throw new Error('日期格式必须是 YYYY-MM-DD')
      }

      const date = new Date(dateString + 'T00:00:00')
      if (isNaN(date.getTime())) {
        throw new Error('无效的日期')
      }

      return date
    },
    /**
     * 组件初始化
     */
    onLoad() {
      this.updateCommonTypes()
      this.initFormData()
    },

    /**
     * 初始化表单数据
     */
    initFormData() {
      const { isEdit, editItem } = this.data

      let formData = {
        type: '',
        amount: '',
        description: ''
      }

      // 如果是编辑模式，填充现有数据
      if (isEdit && editItem) {
        console.log('编辑模式，填充数据:', editItem)
        formData = {
          type: editItem.type || '',
          amount: editItem.amount ? editItem.amount.toString() : '',
          description: editItem.desc || ''  // 直接使用新的 desc 字段
        }
      }

      this.setData({
        formData,
        errors: {}
      })

      // 编辑模式下不需要设置默认描述
    },

    /**
     * 更新类型选项
     */
    updateCommonTypes() {
      const mode = this.data.mode
      let commonTypes = []

      if (mode === 'income') {
        commonTypes = [
          '销售提成', '绩效奖金', '交通补贴', '餐饮补贴',
          '加班费', '全勤奖', '项目奖金', '年终奖'
        ]
      } else if (mode === 'deduction') {
        commonTypes = [
          '迟到扣款', '早退扣款', '缺勤扣款', '违规罚款',
          '社保扣款', '公积金扣款', '个税扣款', '其他扣款'
        ]
      }

      this.setData({
        commonTypes: commonTypes
      })
    },



    /**
     * 类型输入变化
     */
    onTypeInput(e) {
      const value = e.detail.value
      this.setData({
        'formData.type': value,
        'errors.type': ''  // 清除错误信息
      })
    },

    /**
     * 点击常见类型
     */
    onCommonTypeTap(e) {
      const type = e.currentTarget.dataset.type
      console.log('选择常见类型:', type)
      this.setData({
        'formData.type': type,
        'errors.type': ''  // 清除错误信息
      })
    },

    /**
     * 金额输入
     */
    onAmountInput(e) {
      let value = e.detail.value

      // 限制金额格式：最多两位小数
      if (value) {
        // 只允许数字和一个小数点
        value = value.replace(/[^\d.]/g, '')

        // 确保只有一个小数点
        const parts = value.split('.')
        if (parts.length > 2) {
          value = parts[0] + '.' + parts.slice(1).join('')
        }

        // 限制小数位数为2位
        if (parts.length === 2 && parts[1].length > 2) {
          value = parts[0] + '.' + parts[1].substring(0, 2)
        }

        // 防止以小数点开头
        if (value.startsWith('.')) {
          value = '0' + value
        }
      }

      this.setData({
        'formData.amount': value,
        'errors.amount': ''
      })
    },

    /**
     * 描述输入
     */
    onDescriptionInput(e) {
      const value = e.detail.value
      this.setData({
        'formData.description': value,
        'errors.description': ''
      })
    },



    /**
     * 验证表单
     */
    validateForm() {
      const { formData } = this.data
      const errors = {}

      // 验证类型
      if (!formData.type || formData.type.trim() === '') {
        errors.type = '请输入类型'
      }

      // 验证金额
      if (!formData.amount || formData.amount.trim() === '') {
        errors.amount = '请输入金额'
      } else {
        const amount = parseFloat(formData.amount)
        if (isNaN(amount) || amount <= 0) {
          errors.amount = '请输入有效的金额'
        }
      }

      this.setData({ errors })
      return Object.keys(errors).length === 0
    },

    /**
     * 确定按钮点击
     */
    async onConfirm() {
      if (!this.validateForm()) {
        return
      }

      this.setData({ loading: true })

      try {
        const { formData, mode, isEdit, editItem } = this.data
        console.log('收入调整模态框：确认提交', {
          mode,
          isEdit,
          editItem,
          formData
        })

        // 组件自己保存数据
        await this._saveIncomeAdjustment()

        wx.showToast({
          title: isEdit ? '修改成功' : '添加成功',
          icon: 'success'
        })

        // 通知页面数据已更新
        this.triggerEvent('dataUpdated', {
          mode,
          dateString: this.properties.dateString,
          isEdit,
          itemId: editItem ? editItem.id : null
        })

      } catch (error) {
        console.error('收入调整模态框：保存失败', error)
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        })
      } finally {
        this.setData({ loading: false })
      }
    },

    /**
     * 保存收入调整数据
     */
    async _saveIncomeAdjustment() {
      if (!this.incomeAdjustmentService || !this.properties.dateString) {
        throw new Error('收入调整服务或日期不可用')
      }

      const { formData, mode, isEdit, editItem } = this.data
      const date = new Date(this.properties.dateString)

      const type = formData.type ? formData.type.trim() : ''
      const amount = parseFloat(formData.amount)
      const description = formData.description ? formData.description.trim() : ''

      if (isEdit && editItem) {
        // 编辑模式：更新现有项目
        if (mode === 'income') {
          this.incomeAdjustmentService.editExtraIncome(date, editItem, type, amount, description)
        } else {
          this.incomeAdjustmentService.editDeduction(date, editItem, type, amount, description)
        }
      } else {
        // 新增模式：添加新项目
        if (mode === 'income') {
          this.incomeAdjustmentService.addExtraIncome(date, type, amount, description)
        } else {
          this.incomeAdjustmentService.addDeduction(date, type, amount, description)
        }
      }

      console.log('收入调整模态框：保存成功', {
        mode,
        type,
        amount,
        description,
        isEdit
      })
    },

    /**
     * 生成唯一ID
     */
    _generateId() {
      return 'adj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    /**
     * 重置表单
     */
    resetForm() {
      this.setData({
        formData: {
          type: '',
          amount: '',
          description: ''
        },
        errors: {}
      })
    },

    /**
     * 关闭模态框
     */
    onClose() {
      // 开始出场动画（通过移除show类）
      this.triggerEvent('close')
      // 重置表单在父组件关闭后进行
    },

    /**
     * 遮罩层点击
     */
    onOverlayTap() {
      this.onClose()
    },

    /**
     * 模态框内容点击（阻止冒泡）
     */
    onModalTap() {
      // 阻止事件冒泡
    },

    /**
     * 初始化服务
     */
    _initializeServices() {
      const app = getApp()
      this.dataManager = app.getDataManager()
      this.incomeAdjustmentService = app.getIncomeAdjustmentService()
    },

    /**
     * 初始化模态框
     */
    _initializeModal() {
      // 确保服务可用
      if (!this.dataManager || !this.incomeAdjustmentService) {
        console.warn('收入调整模态框：服务未就绪，重新初始化服务')
        this._initializeServices()
      }

      // 如果有编辑项目ID，加载编辑项目
      if (this.properties.editItemId) {
        this._loadEditItem(this.properties.editItemId)
      } else {
        // 重置为新增模式
        this.setData({
          isEdit: false,
          editItem: null
        })
        this.initFormData()
      }
    },

    /**
     * 加载编辑项目
     */
    _loadEditItem(editItemId) {
      if (!editItemId || !this.dataManager) {
        this.setData({
          isEdit: false,
          editItem: null
        })
        return
      }

      try {
        // 从数据管理器加载编辑项目
        const editItem = this._findIncomeAdjustmentItem(editItemId)

        if (editItem) {
          this.setData({
            isEdit: true,
            editItem,
            formData: {
              type: editItem.type || '',
              amount: editItem.amount ? editItem.amount.toString() : '',
              description: editItem.description || ''
            }
          })
          console.log('收入调整模态框：加载编辑项目成功', editItem)
        } else {
          console.warn('收入调整模态框：未找到编辑项目', editItemId)
          this.setData({
            isEdit: false,
            editItem: null
          })
        }
      } catch (error) {
        console.error('收入调整模态框：加载编辑项目失败', error)
        this.setData({
          isEdit: false,
          editItem: null
        })
      }
    },

    /**
     * 查找收入调整项目
     */
    _findIncomeAdjustmentItem(itemId) {
      if (!this.dataManager || !this.properties.dateString) {
        return null
      }

      try {
        // 获取当前工作ID
        const currentWork = this.dataManager.getCurrentWork()
        if (!currentWork) {
          return null
        }

        // 获取指定日期的数据
        const date = new Date(this.properties.dateString)
        const dayData = this.dataManager.getDayData(currentWork.id, date)

        // 在额外收入和扣款中查找
        const allItems = [
          ...(dayData.extraIncomes || []),
          ...(dayData.deductions || [])
        ]

        // 根据ID查找项目（ID可能是数字或字符串）
        return allItems.find(item => item.id == itemId) || null
      } catch (error) {
        console.error('收入调整模态框：查找项目失败', error)
        return null
      }
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('收入调整模态框组件已加载')
      this._initializeServices()
      this.onLoad()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'mode': function(newMode) {
      this.updateCommonTypes()
      this.initFormData()
    },
    'visible': function(visible) {
      if (visible) {
        this._initializeModal()
      }
    },
    'editItemId': function(editItemId) {
      this._loadEditItem(editItemId)
    },
    'dateString': function(newDateString) {
      console.log('收入调整模态框：日期字符串变化:', newDateString)
    }
  }
})
