/* 设置工作计划模态框组件样式 */

/* 模态框基础样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30rpx);
  border-radius: 24rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 90vh;
  overflow: hidden;
  transform: scale(0.9) translateY(50rpx);
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.modal.show .modal-content {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F1F5F9;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
}

.modal-close {
  font-size: 48rpx;
  color: #9CA3AF;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:active {
  background-color: #F3F4F6;
}

.modal-body {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: scroll;
}

.modal-date {
  font-size: 32rpx;
  color: #2d3748;
  margin-bottom: 32rpx;
  text-align: center;
}

/* 输入组样式 */
.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}

.input-tip {
  font-size: 24rpx;
  color: #6B7280;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 状态选择器 */
.status-selector {
  background-color: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.2s ease;
}

.status-selector:active {
  background-color: #F3F4F6;
}

.picker-text {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #374151;
}

.status-emoji {
  font-size: 32rpx;
}

.selector-arrow {
  margin-left: auto;
  font-size: 32rpx;
  color: #9CA3AF;
}

/* 智能填写按钮 */
.time-summary {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.smart-income-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.smart-income-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 24rpx;
}

/* 冲突警告 */
.conflict-warning {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background-color: #FEF2F2;
  border: 1rpx solid #FECACA;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.warning-icon {
  font-size: 28rpx;
}

.warning-text {
  font-size: 24rpx;
  color: #DC2626;
  flex: 1;
}

/* 时间段卡片 */
.time-inputs {
  margin-bottom: 20rpx;
}

.time-segment-card {
  background-color: #FFFFFF;
  border: 1rpx solid #E5E7EB;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.time-segment-card.conflict {
  border-color: #EF4444;
  background-color: #FEF2F2;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}

/* 时间段头部 */
.segment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.segment-type-picker {
  flex: 1;
}

.segment-type-display {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background-color: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #374151;
}

.type-icon {
  font-size: 28rpx;
}

.type-text {
  font-weight: 500;
}

.segment-duration {
  margin: 0 16rpx;
}

.duration-value {
  font-size: 24rpx;
  color: #6B7280;
}

.segment-remove-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #FEF2F2;
  border: 1rpx solid #FECACA;
  border-radius: 50%;
  color: #DC2626;
  font-size: 32rpx;
  transition: all 0.2s ease;
}

.segment-remove-btn:active {
  background-color: #FEE2E2;
  transform: scale(0.9);
}

/* 时间设置行 */
.time-setting-row {
  margin-bottom: 20rpx;
}

.time-range-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.time-range-selector:active {
  background-color: #F3F4F6;
}

.time-range-display {
  flex: 1;
}

.time-range-text {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 8rpx;
}

.start-time, .end-time {
  font-weight: 600;
}

.start-next-day-indicator, .end-next-day-indicator {
  font-size: 20rpx;
  color: #F59E0B;
  background-color: #FEF3C7;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
}

.time-separator {
  color: #9CA3AF;
  margin: 0 8rpx;
}

.time-range-duration {
  font-size: 24rpx;
  color: #6B7280;
}

.time-range-arrow {
  margin-left: 16rpx;
}

.arrow-icon {
  font-size: 32rpx;
  color: #9CA3AF;
}

/* 收入设置行 */
.income-setting-row {
  display: flex;
  gap: 24rpx;
}

.income-field, .hourly-rate-field {
  flex: 1;
}

.income-field-label, .hourly-rate-label {
  display: block;
  font-size: 24rpx;
  color: #6B7280;
  margin-bottom: 8rpx;
}

.income-input-wrapper, .hourly-rate-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #F9FAFB;
  border: 1rpx solid #E5E7EB;
  border-radius: 8rpx;
  padding: 0 16rpx;
  transition: all 0.2s ease;
}

.income-input-wrapper:focus-within, .hourly-rate-input-wrapper:focus-within {
  border-color: #3B82F6;
  background-color: #FFFFFF;
}

.income-input-field, .hourly-rate-input-field {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #374151;
  background: transparent;
  border: none;
  outline: none;
}

.income-unit, .hourly-rate-unit {
  font-size: 24rpx;
  color: #6B7280;
  margin-left: 8rpx;
}

/* 时间段警告 */
.segment-warning {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 12rpx 16rpx;
  background-color: #FEF2F2;
  border: 1rpx solid #FECACA;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.warning-message {
  font-size: 24rpx;
  color: #DC2626;
  flex: 1;
}

/* 添加时间段按钮 */
.add-time-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  background-color: #F9FAFB;
  border: 2rpx dashed #D1D5DB;
  border-radius: 12rpx;
  color: #6B7280;
  font-size: 28rpx;
  transition: all 0.2s ease;
}

.add-time-btn:active {
  background-color: #F3F4F6;
  border-color: #9CA3AF;
}

.add-icon {
  font-size: 32rpx;
}

/* 统计信息区域 */
.schedule-summary {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 20rpx 24rpx;
  background-color: #F8FAFC;
  border-top: 1rpx solid #E2E8F0;
  border-bottom: 1rpx solid #E2E8F0;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #64748B;
  font-weight: 500;
}

.summary-value {
  font-size: 28rpx;
  color: #1E293B;
  font-weight: 600;
}

.income-item .summary-value {
  color: #059669;
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #F1F5F9;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-secondary {
  background-color: #F8FAFC;
  color: #475569;
  border: 1rpx solid #E2E8F0;
}

.btn-secondary:active {
  background-color: #F1F5F9;
}

.btn-primary {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  border: none;
}

.btn-primary:active {
  transform: scale(0.98);
}

.btn-primary.disabled {
  background: #E5E7EB;
  color: #9CA3AF;
  cursor: not-allowed;
}

.btn-primary.disabled:active {
  transform: none;
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 750rpx) {
  .time-segment-card {
    margin-bottom: 16rpx;
    padding: 20rpx;
  }

  .segment-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16rpx;
  }

  .segment-type-display {
    justify-content: center;
  }

  .segment-duration {
    text-align: center;
    margin: 0;
  }

  .segment-remove-btn {
    align-self: center;
  }

  .time-range-text {
    flex-direction: column;
    align-items: center;
    gap: 4rpx;
  }

  .income-setting-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .schedule-summary {
    padding: 16rpx 20rpx;
  }

  .summary-label {
    font-size: 20rpx;
  }

  .summary-value {
    font-size: 24rpx;
  }

  .modal-footer {
    flex-direction: column;
    gap: 12rpx;
  }

  .modal-btn {
    height: 80rpx;
  }
}
