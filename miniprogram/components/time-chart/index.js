// 时间可视化图表组件
const { formatDuration, calculateHourlyRate, minutesToTimeDisplay } = require('../../utils/helpers/time-utils.js')

Component({
  properties: {
    // 要显示的时间段数据
    segments: {
      type: Array,
      value: [],
      observer: 'updateChart'
    },

    // 摸鱼数据
    fishes: {
      type: Array,
      value: [],
      observer: 'updateChart'
    },

    // 当前摸鱼状态
    currentFishingState: {
      type: Object,
      value: null,
      observer: 'updateChart'
    },

    // 选中的日期
    selectedDate: {
      type: String,
      value: '',
      observer: 'updateChart'
    },

    // 图表高度
    height: {
      type: Number,
      value: 120
    },

    // 是否显示当前时间线
    showCurrentTime: {
      type: Boolean,
      value: true
    },

    // 是否正在使用昨天的数据
    isUsingYesterdayData: {
      type: Boolean,
      value: false,
      observer: 'updateChart'
    },

    // 工作日期显示文本
    workDateDisplayText: {
      type: String,
      value: '今日',
      observer: 'updateChart'
    }
  },

  data: {
    // 可视化数据
    visualSegments: [],
    visualFishes: [], // 摸鱼可视化数据
    timeScale: [],
    currentTimePosition: -1,

    // 统计信息
    totalDuration: '0小时',
    workRatio: 0,
    fishingRatio: 0, // 摸鱼比例

    // 图表尺寸
    chartWidth: 100,
    chartHeight: 120,

    // 显示模式
    viewMode: 'smart', // 'smart': 智能范围（时间段范围），'full': 全天范围（0-24小时）
    timeRange: null // 当前使用的时间范围
  },

  lifetimes: {
    attached() {
      this.updateChart()
      this.startAutoRefresh()
    },

    detached() {
      this.stopAutoRefresh()
    }
  },

  methods: {
    /**
     * 更新图表
     */
    updateChart() {
      // console.log('时间图表更新开始，segments数量：', this.data.segments ? this.data.segments.length : 0)
      
      if (!this.data.segments || this.data.segments.length === 0) {
        // 空状态时使用全天范围
        const timeRange = {
          start: new Date().setHours(0, 0, 0, 0),
          end: new Date().setHours(23, 59, 59, 999),
          mode: 'full'
        }
        this.setData({
          visualSegments: [],
          timeScale: this.generateTimeScale(timeRange),
          currentTimePosition: -1,
          totalDuration: '0小时',
          workRatio: 0,
          timeRange: timeRange
        })
        
        // 重新启动自动刷新
        this.startAutoRefresh()
        return
      }

      // 检查数据格式并修复
      const segments = this.processSegments()
      if (segments.length === 0) {
        console.warn('时间段数据处理后为空')
        // 数据处理后为空时使用全天范围
        const timeRange = {
          start: new Date().setHours(0, 0, 0, 0),
          end: new Date().setHours(23, 59, 59, 999),
          mode: 'full'
        }
        this.setData({
          visualSegments: [],
          timeScale: this.generateTimeScale(timeRange),
          currentTimePosition: -1,
          totalDuration: '0小时',
          workRatio: 0,
          timeRange: timeRange
        })
        
        // 重新启动自动刷新
        this.startAutoRefresh()
        return
      }

      const timeRange = this.calculateTimeRange()
      const visualSegments = this.generateVisualSegments(timeRange, segments)
      const visualFishes = this.generateVisualFishes(timeRange, this.data.fishes || [], this.data.currentFishingState)
      const timeScale = this.generateTimeScale(timeRange)
      const currentTimePosition = this.calculateCurrentTimePosition(timeRange)
      const stats = this.calculateStats(segments, this.data.fishes || [])

      this.setData(Object.assign({
        visualSegments,
        visualFishes,
        timeScale,
        currentTimePosition,
        chartHeight: this.data.height,
        timeRange: timeRange
      }, stats))

      // 重新启动自动刷新（因为时间范围可能变化）
      this.startAutoRefresh()

      // console.log('时间图表更新完成')
    },

    /**
     * 处理时间段数据，确保格式正确
     */
    processSegments() {
      const segments = this.data.segments || []
      // console.log('原始segments数据：', segments)
      
      const processedSegments = segments.map((segment, index) => {
        // console.log(`处理第${index}个时间段:`, segment)
        // console.log('检测到时间段（分钟数格式）')

        return {
          ...segment,
          startTime: segment.startTime || minutesToTimeDisplay(segment.start),
          endTime: segment.endTime || minutesToTimeDisplay(segment.end)
        }
        
        return Object.assign({}, segment, {
          start: start,
          end: end
        })
      }).filter(segment => segment !== null)
      
      // console.log('处理后的segments数据：', processedSegments)
      return processedSegments
    },

    /**
     * 处理时间段数据用于时间范围计算
     */
    processSegmentsForTimeRange(segments, baseDate) {
      if (!baseDate) {
        baseDate = new Date()
      }

      // 确保使用正确的基准日期
      const dayStart = new Date(baseDate)
      dayStart.setHours(0, 0, 0, 0)

      return segments.map(segment => {
        const start = new Date(dayStart.getTime() + segment.start * 60 * 1000)
        const end = new Date(dayStart.getTime() + segment.end * 60 * 1000)

        return { start, end }
      })
    },

    /**
     * 计算时间范围
     */
    calculateTimeRange() {
      const segments = this.data.segments || []
      const viewMode = this.data.viewMode
      
      if (!this.data.selectedDate) {
        const now = new Date()
        return {
          start: new Date(now).setHours(0, 0, 0, 0),
          end: new Date(now).setHours(23, 59, 59, 999),
          mode: 'full'
        }
      }
      
      const date = new Date(this.data.selectedDate)
      
      // 全天模式：总是显示0-24小时（或跨日情况下的48小时）
      if (viewMode === 'full') {
        return this.calculateFullTimeRange(date, segments)
      }
      
      // 智能模式：根据时间段范围计算
      return this.calculateSmartTimeRange(date, segments)
    },

    /**
     * 计算全天时间范围（0-24小时或跨日48小时）
     */
    calculateFullTimeRange(date, segments) {
      let start = new Date(date).setHours(0, 0, 0, 0)
      let end = new Date(date).setHours(23, 59, 59, 999)
      
      // 检查是否有跨日的时间段
      let hasNextDaySegments = false
      
      if (segments && segments.length > 0) {
        const processedSegments = this.processSegmentsForTimeRange(segments, date)

        for (const segment of processedSegments) {
          const segmentStart = segment.start
          const segmentEnd = segment.end
          
          if (segmentEnd && segmentStart) {
            const segmentDate = new Date(segmentStart.getFullYear(), segmentStart.getMonth(), segmentStart.getDate())
            const endDate = new Date(segmentEnd.getFullYear(), segmentEnd.getMonth(), segmentEnd.getDate())
            const selectedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
            
            if (segmentDate.getTime() === selectedDate.getTime() && endDate.getTime() > selectedDate.getTime()) {
              hasNextDaySegments = true
              break
            }
          }
        }
      }
      
      // 如果有跨日时间段，扩展时间范围到次日24:00
      if (hasNextDaySegments) {
        const nextDay = new Date(date)
        nextDay.setDate(nextDay.getDate() + 1)
        end = new Date(nextDay).setHours(23, 59, 59, 999)
      }
      
      return { start, end, mode: 'full' }
    },

    /**
     * 计算智能时间范围（基于时间段的实际范围）
     */
    calculateSmartTimeRange(date, segments) {
      if (!segments || segments.length === 0) {
        // 没有时间段时，显示全天
        return this.calculateFullTimeRange(date, segments)
      }
      
      const processedSegments = this.processSegmentsForTimeRange(segments, date)
      if (processedSegments.length === 0) {
        return this.calculateFullTimeRange(date, segments)
      }
      
      // 找到最早和最晚的时间
      let earliestTime = null
      let latestTime = null
      
      processedSegments.forEach(segment => {
        if (!earliestTime || segment.start < earliestTime) {
          earliestTime = segment.start
        }
        if (!latestTime || segment.end > latestTime) {
          latestTime = segment.end
        }
      })
      
      if (!earliestTime || !latestTime) {
        return this.calculateFullTimeRange(date, segments)
      }
      
      // 添加一些缓冲时间，让显示更舒适
      const bufferMinutes = 0 // 前后各增加 n 分钟缓冲
      const start = new Date(earliestTime.getTime() - bufferMinutes * 60 * 1000)
      const end = new Date(latestTime.getTime() + bufferMinutes * 60 * 1000)
      
      // 确保开始时间不早于当天0点
      const dayStart = new Date(date).setHours(0, 0, 0, 0)
      const finalStart = Math.max(start.getTime(), dayStart)
      
      // 如果结束时间超过了次日，需要处理跨日情况
      const dayEnd = new Date(date).setHours(23, 59, 59, 999)
      let finalEnd = end.getTime()
      
      // 如果结束时间超过当天23:59，说明跨日了
      if (finalEnd > dayEnd) {
        const nextDay = new Date(date)
        nextDay.setDate(nextDay.getDate() + 1)
        const nextDayEnd = new Date(nextDay).setHours(23, 59, 59, 999)
        finalEnd = Math.min(finalEnd, nextDayEnd)
      }
      
      // console.log('智能时间范围:', new Date(finalStart), '到', new Date(finalEnd))
      
      return { 
        start: finalStart, 
        end: finalEnd, 
        mode: 'smart',
        originalEarliest: earliestTime,
        originalLatest: latestTime
      }
    },

    /**
     * 生成可视化时间段
     */
    generateVisualSegments(timeRange, segments) {
      const totalDuration = timeRange.end - timeRange.start

      return segments.map(segment => {
        const duration = segment.end - segment.start

        // 将分钟数转换为时间戳用于位置计算
        const baseDate = new Date(timeRange.start)
        baseDate.setHours(0, 0, 0, 0)

        const startTime = new Date(baseDate.getTime() + segment.start * 60 * 1000)
        const endTime = new Date(baseDate.getTime() + segment.end * 60 * 1000)

        const segmentStart = Math.max(startTime.getTime(), timeRange.start)
        const segmentEnd = Math.min(endTime.getTime(), timeRange.end)

        const left = ((segmentStart - timeRange.start) / totalDuration) * 100
        const width = ((segmentEnd - segmentStart) / totalDuration) * 100
        
        return {
          id: segment.id,
          type: segment.type,
          typeText: this.getTypeText(segment.type),
          duration: formatDuration(duration),
          left: left,
          width: Math.max(width, 0.5), // 确保最小宽度
          color: this.getTypeColor(segment.type),
          isCurrent: this.isCurrentSegment(segment)
        }
      }).filter(segment => segment !== null) // 过滤掉无效的时间段
    },

    /**
     * 生成摸鱼可视化数据
     * @param {Object} timeRange - 时间范围
     * @param {Array} fishes - 已完成的摸鱼记录
     * @param {Object} currentFishingState - 当前摸鱼状态
     */
    generateVisualFishes(timeRange, fishes, currentFishingState) {
      const visualFishes = []
      const totalDuration = timeRange.end - timeRange.start

      // 1. 处理已完成的摸鱼记录
      if (fishes && fishes.length > 0) {
        fishes.forEach(fish => {
          const visualFish = this.createVisualFish(fish, timeRange, totalDuration, false)
          if (visualFish) {
            visualFishes.push(visualFish)
          }
        })
      }

      // 2. 处理当前正在进行的摸鱼
      if (currentFishingState && currentFishingState.isActive) {
        const currentFish = this.createCurrentFishingVisual(currentFishingState, timeRange, totalDuration)
        if (currentFish) {
          visualFishes.push(currentFish)
        }
      }

      return visualFishes
    },

    /**
     * 创建摸鱼可视化对象
     */
    createVisualFish(fish, timeRange, totalDuration, isCurrent = false) {
      // 将分钟数转换为时间戳用于位置计算
      const baseDate = new Date(timeRange.start)
      baseDate.setHours(0, 0, 0, 0)

      const startTime = new Date(baseDate.getTime() + fish.start * 60 * 1000)
      const endTime = new Date(baseDate.getTime() + fish.end * 60 * 1000)

      const fishStart = Math.max(startTime.getTime(), timeRange.start)
      const fishEnd = Math.min(endTime.getTime(), timeRange.end)

      const left = ((fishStart - timeRange.start) / totalDuration) * 100
      const width = ((fishEnd - fishStart) / totalDuration) * 100

      // 如果宽度太小，不显示
      if (width <= 0) {
        return null
      }

      return {
        id: fish.id,
        startTime: minutesToTimeDisplay(fish.start),
        endTime: minutesToTimeDisplay(fish.end),
        duration: formatDuration(fish.end - fish.start),
        remark: fish.remark || '',
        left: left,
        width: Math.max(width, 0.5), // 确保最小宽度
        color: isCurrent ? '#f59e0b' : '#fbbf24', // 当前摸鱼使用更深的颜色
        opacity: isCurrent ? 0.9 : 0.8, // 当前摸鱼更不透明
        isCurrent: isCurrent
      }
    },

    /**
     * 创建当前摸鱼的可视化对象
     */
    createCurrentFishingVisual(fishingState, timeRange, totalDuration) {
      if (!fishingState || !fishingState.isActive) {
        return null
      }

      const now = new Date()
      const startTime = new Date(fishingState.startTime)

      // 将当前摸鱼转换为分钟数格式
      const startMinutes = fishingState.startMinutes || (startTime.getHours() * 60 + startTime.getMinutes())
      const currentMinutes = now.getHours() * 60 + now.getMinutes()

      // 创建临时的摸鱼对象
      const currentFish = {
        id: 'current-fishing',
        start: startMinutes,
        end: currentMinutes,
        remark: fishingState.remark || '摸鱼中...'
      }

      return this.createVisualFish(currentFish, timeRange, totalDuration, true)
    },

    /**
     * 生成时间刻度
     */
    generateTimeScale(timeRange) {
      const scale = []
      
      if (!timeRange) {
        return scale
      }
      
      const totalDuration = timeRange.end - timeRange.start
      const totalHours = totalDuration / (1000 * 60 * 60)
      
      // 根据时间范围模式生成不同的刻度
      if (timeRange.mode === 'smart') {
        // 智能模式：优先使用基于时间段的刻度
        return this.generateSegmentBasedTimeScale(timeRange, totalDuration)
      } else {
        return this.generateFullTimeScale(timeRange, totalHours)
      }
    },

    /**
     * 生成基于时间段的时间刻度（去重时间点）
     */
    generateSegmentBasedTimeScale(timeRange, totalDuration) {
      const segments = this.data.segments || []

      if (!segments || segments.length === 0) {
        // 没有时间段时，回退到原有的智能刻度
        return this.generateSmartTimeScale(timeRange, totalDuration)
      }

      // 收集所有时间段的开始和结束时间点（分钟数）
      const timePoints = new Set()

      segments.forEach(segment => {
        timePoints.add(segment.start)
        timePoints.add(segment.end)
      })

      // 转换为数组并排序
      const sortedTimePoints = Array.from(timePoints).sort((a, b) => a - b)

      // 将分钟数转换为时间刻度
      const scale = []
      const baseDate = new Date(timeRange.start)
      baseDate.setHours(0, 0, 0, 0)

      sortedTimePoints.forEach(minutes => {
        const timeStamp = new Date(baseDate.getTime() + minutes * 60 * 1000)

        // 检查时间点是否在显示范围内
        if (timeStamp.getTime() >= timeRange.start && timeStamp.getTime() <= timeRange.end) {
          const position = ((timeStamp.getTime() - timeRange.start) / totalDuration) * 100

          scale.push({
            hour: minutes / 60,
            text: this.formatTimeForScale(timeStamp, timeRange),
            position: Math.max(0, Math.min(100, position))
          })
        }
      })

      // 如果刻度点太少，补充一些中间刻度以提高可读性
      if (scale.length < 3) {
        return this.generateSmartTimeScale(timeRange, totalDuration)
      }

      return scale
    },

    /**
     * 生成智能时间刻度（基于实际时间段范围）- 备用方法
     */
    generateSmartTimeScale(timeRange, totalDuration) {
      const scale = []
      const startTime = new Date(timeRange.start)
      const endTime = new Date(timeRange.end)
      
      // 根据时间跨度决定刻度间隔
      const totalHours = totalDuration / (1000 * 60 * 60)
      let intervalHours
      
      if (totalHours <= 4) {
        intervalHours = 1 // 1小时间隔
      } else if (totalHours <= 12) {
        intervalHours = 2 // 2小时间隔
      } else {
        intervalHours = 3 // 3小时间隔
      }
      
      // 找到合适的起始小时（向下取整到间隔的倍数）
      const startHour = Math.floor(startTime.getHours() / intervalHours) * intervalHours
      
      // 创建第一个刻度点
      const firstTickTime = new Date(startTime)
      firstTickTime.setHours(startHour, 0, 0, 0)
      
      let currentTickTime = new Date(firstTickTime)
      
      // 如果第一个刻度点早于实际开始时间，调整到开始时间
      if (currentTickTime < startTime) {
        currentTickTime = new Date(startTime)
      }
      
      // 生成刻度点
      while (currentTickTime <= endTime) {
        const position = ((currentTickTime.getTime() - timeRange.start) / totalDuration) * 100
        
        // 格式化时间文本
        let text = this.formatTimeForScale(currentTickTime, timeRange)
        
        scale.push({
          hour: currentTickTime.getHours() + (currentTickTime.getMinutes() / 60),
          text: text,
          position: Math.max(0, Math.min(100, position))
        })
        
        // 移动到下一个刻度点
        currentTickTime.setHours(currentTickTime.getHours() + intervalHours)
      }
      
      // 确保有结束时间刻度
      const endPosition = ((endTime.getTime() - timeRange.start) / totalDuration) * 100
      if (endPosition <= 100 && (scale.length === 0 || scale[scale.length - 1].position < 95)) {
        scale.push({
          hour: endTime.getHours() + (endTime.getMinutes() / 60),
          text: this.formatTimeForScale(endTime, timeRange),
          position: 100
        })
      }
      
      return scale
    },

    /**
     * 生成全天时间刻度（0-24小时或跨日48小时）
     */
    generateFullTimeScale(timeRange, totalHours) {
      const scale = []
      
      // 如果是跨日显示（48小时），使用不同的刻度间隔
      if (totalHours > 24) {
        // 跨日显示：0:00, 6:00, 12:00, 18:00, 24:00(次日0:00), 30:00(次日6:00), 36:00(次日12:00), 42:00(次日18:00), 48:00(次日24:00)
        for (let hour = 0; hour <= 48; hour += 6) {
          let text
          if (hour <= 24) {
            text = hour === 24 ? '24:00' : `${hour.toString().padStart(2, '0')}:00`
          } else {
            const nextDayHour = hour - 24
            text = nextDayHour === 24 ? '次日24:00' : `次日${nextDayHour.toString().padStart(2, '0')}:00`
          }
          
          scale.push({
            hour,
            text,
            position: (hour / 48) * 100
          })
        }
      } else {
        // 单日显示：0:00, 6:00, 12:00, 18:00, 24:00
        for (let hour = 0; hour <= 24; hour += 6) {
          scale.push({
            hour,
            text: hour === 24 ? '24:00' : `${hour.toString().padStart(2, '0')}:00`,
            position: (hour / 24) * 100
          })
        }
      }
      
      return scale
    },

    /**
     * 格式化时间刻度文本
     */
    formatTimeForScale(time, timeRange) {
      const hour = time.getHours().toString().padStart(2, '0')
      const minute = time.getMinutes().toString().padStart(2, '0')

      // 如果使用昨天数据，需要特殊的时间标签
      if (this.data.isUsingYesterdayData) {
        // 检查是否跨日
        const rangeStart = new Date(timeRange.start)
        const timeDate = new Date(time.getFullYear(), time.getMonth(), time.getDate())
        const rangeDate = new Date(rangeStart.getFullYear(), rangeStart.getMonth(), rangeStart.getDate())

        if (timeDate.getTime() > rangeDate.getTime()) {
          // 次日的时间，不需要前缀（因为这是"今天"）
          return `${hour}:${minute}`
        } else {
          // 昨天的时间，需要"昨日"前缀
          return `昨日${hour}:${minute}`
        }
      } else {
        // 普通情况：检查是否跨日
        const rangeStart = new Date(timeRange.start)
        const timeDate = new Date(time.getFullYear(), time.getMonth(), time.getDate())
        const rangeDate = new Date(rangeStart.getFullYear(), rangeStart.getMonth(), rangeStart.getDate())

        if (timeDate.getTime() > rangeDate.getTime()) {
          return `次日${hour}:${minute}`
        } else {
          return `${hour}:${minute}`
        }
      }
    },

    /**
     * 计算当前时间位置
     */
    calculateCurrentTimePosition(timeRange) {
      if (!this.data.showCurrentTime || !this.data.selectedDate) {
        return -1
      }

      const now = new Date()
      const selectedDate = new Date(this.data.selectedDate)

      // 如果使用昨天数据，需要特殊处理
      if (this.data.isUsingYesterdayData) {
        // 使用昨天数据时，当前时间应该显示在跨日期时间段的正确位置
        const currentMinutes = now.getHours() * 60 + now.getMinutes()

        // 将当前时间转换为基于昨天的时间戳
        const baseDate = new Date(timeRange.start)
        baseDate.setHours(0, 0, 0, 0)

        // 当前时间在次日，所以需要加上24小时
        const currentTimeInRange = new Date(baseDate.getTime() + (currentMinutes + 1440) * 60 * 1000)

        const totalDuration = timeRange.end - timeRange.start
        const position = ((currentTimeInRange.getTime() - timeRange.start) / totalDuration) * 100

        // 确保位置在有效范围内
        if (position >= 0 && position <= 100) {
          return position
        }

        return -1
      }

      // 普通情况：检查是否是同一天
      if (now.toDateString() !== selectedDate.toDateString()) {
        return -1
      }

      const currentTime = now.getTime()
      const totalDuration = timeRange.end - timeRange.start
      const position = ((currentTime - timeRange.start) / totalDuration) * 100

      // 确保位置在有效范围内
      if (position >= 0 && position <= 100) {
        return position
      }

      return -1
    },

    /**
     * 计算统计信息
     */
    calculateStats(segments, fishes = []) {
      if (!segments || segments.length === 0) {
        return {
          totalDuration: '0小时',
          workRatio: 0,
          fishingRatio: 0
        }
      }

      let totalMinutes = 0
      let workMinutes = 0
      let fishingMinutes = 0

      // 计算工作时间段统计
      segments.forEach(segment => {
        const duration = segment.end - segment.start
        totalMinutes += duration

        if (segment.type === 'work' || segment.type === 'overtime') {
          workMinutes += duration
        }
      })

      // 计算摸鱼时间统计
      if (fishes && fishes.length > 0) {
        fishes.forEach(fish => {
          const duration = fish.end - fish.start
          fishingMinutes += duration
        })
      }

      const totalDuration = formatDuration(totalMinutes)
      const workRatio = totalMinutes > 0 ? Math.round((workMinutes / totalMinutes) * 100) : 0
      const fishingRatio = workMinutes > 0 ? Math.round((fishingMinutes / workMinutes) * 100) : 0

      return {
        totalDuration,
        workRatio,
        fishingRatio
      }
    },

    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const typeMap = {
        work: '工作',
        rest: '休息',
        overtime: '加班'
      }
      return typeMap[type] || '未知'
    },

    /**
     * 获取类型颜色
     */
    getTypeColor(type) {
      const colorMap = {
        work: '#3B82F6',
        rest: '#10B981', 
        overtime: '#f6416c'
      }
      return colorMap[type] || '#6B7280'
    },

    /**
     * 检查是否为当前时间段
     */
    isCurrentSegment(segment) {
      const now = new Date()
      return now >= segment.start && now <= segment.end
    },

    /**
     * 图表点击事件 - 切换显示模式
     */
    onChartTap(e) {
      // console.log('点击图表，切换显示模式')
      
      // 切换显示模式
      const newViewMode = this.data.viewMode === 'smart' ? 'full' : 'smart'
      
      this.setData({
        viewMode: newViewMode
      })
      
      // 重新计算和更新图表
      this.updateChart()
      
      // 触发模式切换事件
      this.triggerEvent('viewModeChange', {
        viewMode: newViewMode
      })
    },

    /**
     * 启动自动刷新
     */
    startAutoRefresh() {
      this.stopAutoRefresh() // 确保没有重复的定时器
      
      if (!this.data.showCurrentTime) {
        return // 如果不显示当前时间，不需要刷新
      }

      // 计算刷新间隔
      const refreshInterval = this.calculateRefreshInterval()
      
      if (refreshInterval > 0) {
        this.refreshTimer = setInterval(() => {
          this.updateCurrentTimePosition()
        }, refreshInterval)
        
        // console.log(`时间图表自动刷新已启动，间隔: ${refreshInterval}ms`)
      }
    },

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
        // console.log('时间图表自动刷新已停止')
      }
    },

    /**
     * 计算刷新间隔
     */
    calculateRefreshInterval() {
      try {
        // 获取屏幕信息
        const windowInfo = wx.getWindowInfo()
        const screenWidth = windowInfo.windowWidth || 375
        const pixelRatio = windowInfo.pixelRatio || 2
        
        // 计算有效像素宽度
        const effectiveWidth = screenWidth * pixelRatio
        
        // 获取时间跨度
        const timeRange = this.data.timeRange
        if (!timeRange) {
          return 60000 // 默认1分钟
        }
        
        const totalDuration = timeRange.end - timeRange.start
        const totalMinutes = totalDuration / (1000 * 60)
        
        // 根据时间跨度和屏幕宽度计算每像素代表的时间
        const timePerPixel = totalDuration / effectiveWidth
        
        // 设置刷新间隔，确保时间线移动流畅
        let refreshInterval
        
        if (totalMinutes <= 60) {
          // 1小时内：高频刷新，确保流畅
          refreshInterval = Math.max(8, timePerPixel / 4)
        } else if (totalMinutes <= 240) {
          // 4小时内：中频刷新
          refreshInterval = Math.max(100, timePerPixel / 2)
        } else if (totalMinutes <= 720) {
          // 12小时内：低频刷新
          refreshInterval = Math.max(1000, timePerPixel)
        } else {
          // 超过12小时：最低频刷新
          refreshInterval = Math.min(60000, Math.max(5000, timePerPixel * 2))
        }
        
        // 确保在合理范围内
        refreshInterval = Math.max(8, Math.min(60000, refreshInterval))
        
        // console.log(`时间图表刷新间隔计算: 时间跨度=${totalMinutes.toFixed(1)}分钟, 屏幕宽度=${screenWidth}px, 像素比=${pixelRatio}, 刷新间隔=${refreshInterval}ms`)
        
        return Math.round(refreshInterval)
      } catch (error) {
        console.error('计算刷新间隔失败:', error)
        return 1000 // 默认1秒
      }
    },

    /**
     * 仅更新当前时间位置（用于定时刷新）
     */
    updateCurrentTimePosition() {
      if (!this.data.timeRange || !this.data.showCurrentTime) {
        return
      }

      const currentTimePosition = this.calculateCurrentTimePosition(this.data.timeRange)
      
      if (currentTimePosition !== this.data.currentTimePosition) {
        this.setData({
          currentTimePosition
        })
      }
    },

  }
})