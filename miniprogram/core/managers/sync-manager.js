/**
 * 数据同步管理器
 *
 * 核心功能：
 * 1. 启动时自动同步：比较 userData.lastModified 时间戳，自动选择最新数据
 * 2. 数据变化时自动上传：5秒防抖机制，避免频繁上传
 * 3. 所有用户均可使用数据同步功能
 */

const { normalizeTimestamp } = require('../../utils/time-utils.js')
const { getRelativeTime } = require('../../utils/helpers/time-utils.js')

class SyncManager {
  constructor() {
    // 单例模式
    if (SyncManager.instance) {
      return SyncManager.instance
    }

    // 同步状态
    this.isInitialized = false
    this.isSyncing = false
    this.lastSyncTime = null
    
    // 管理器引用
    this.dataManager = null
    this.userManager = null
    
    // 变化监听器
    this.changeListeners = []
    this.dataChangeListener = null

    // 防抖上传定时器
    this.uploadTimer = null
    this.uploadDelay = 5000 // 5秒防抖

    // 数据变化监听暂停标志
    this.pauseDataChangeListener = false

    // 本地存储键
    this.STORAGE_KEY = 'SYNC_STATUS'

    SyncManager.instance = this
  }

  /**
   * 获取单例实例
   */
  static getInstance() {
    if (!SyncManager.instance) {
      SyncManager.instance = new SyncManager()
    }
    return SyncManager.instance
  }

  /**
   * 初始化同步管理器
   */
  async initialize(managers) {
    if (this.isInitialized) {
      return
    }

    try {
      console.log('初始化数据同步管理器...')

      this.dataManager = managers.dataManager
      this.userManager = managers.userManager
      
      // 加载同步状态
      this.loadSyncStatus()
      
      // 注册数据变化监听器
      this.registerDataChangeListener()
      
      // 等待用户登录后执行启动同步
      if (this.userManager.isUserLoggedIn()) {
        await this.checkAndSyncOnStartup()
      } else {
        // 延迟检查用户登录状态
        setTimeout(async () => {
          if (this.userManager.isUserLoggedIn()) {
            await this.checkAndSyncOnStartup()
          }
        }, 2000)
      }
      
      this.isInitialized = true
      console.log('数据同步管理器初始化完成')
    } catch (error) {
      console.error('数据同步管理器初始化失败:', error)
      throw error
    }
  }







  /**
   * 获取云端数据信息
   */
  async getCloudData() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'getCloudDataInfo',
          data: {},
        }
      })

      // 处理权限错误
      if (!result.result.success && result.result.needUpgrade) {
        this.showUpgradeModal('数据同步功能')
        return { success: false, error: result.result.errMsg }
      }

      return result.result
    } catch (error) {
      console.error('获取云端数据信息失败:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * 下载并应用云端数据
   */
  async downloadAndApplyData() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'downloadUserData',
          data: {},
        }
      })

      if (result.result.success) {
        const cloudData = result.result.data
        console.log('下载云端数据成功，应用到本地...')

        // 暂停数据变化监听器，避免触发上传
        this.pauseDataChangeListener = true

        // 应用到本地（会触发页面更新）
        this.dataManager.loadUserData(cloudData, false)

        // 延迟恢复监听器，确保数据加载完成
        setTimeout(() => {
          this.pauseDataChangeListener = false
          console.log('数据变化监听器已恢复')
        }, 1000)

        console.log('云端数据应用成功')
        wx.showToast({
          title: '云端数据应用成功',
          icon: 'success'
        })
      } else {
        // 处理权限错误
        if (result.result.needUpgrade) {
          this.showUpgradeModal('数据同步功能')
          return
        }
        throw new Error(result.result.errMsg || '下载数据失败')
      }
    } catch (error) {
      console.error('下载并应用数据失败:', error)
      throw error
    }
  }

  /**
   * 上传本地数据
   */
  async uploadData() {
    try {
      const localData = this.dataManager.getUserData()

      const result = await wx.cloud.callFunction({
        name: 'cloud-functions',
        data: {
          type: 'uploadUserData',
          data: {
            data: localData
          }
        }
      })

      if (result.result.success) {
        console.log('本地数据上传成功')
        wx.showToast({
          title: '数据上传成功',
          icon: 'success'
        })
      } else {
        // 处理权限错误
        if (result.result.needUpgrade) {
          this.showUpgradeModal('数据同步功能')
          return
        }
        throw new Error(result.result.errMsg || '上传数据失败')
      }
    } catch (error) {
      console.error('上传数据失败:', error)
      throw error
    }
  }

  /**
   * 防抖上传数据
   */
  debounceUpload() {
    // 如果监听器被暂停，跳过上传
    if (this.pauseDataChangeListener) {
      console.log('数据变化监听器已暂停，跳过上传')
      return
    }

    // 清除之前的定时器
    if (this.uploadTimer) {
      clearTimeout(this.uploadTimer)
    }

    // 设置新的防抖定时器
    this.uploadTimer = setTimeout(() => {
      this.onDataChange()
    }, this.uploadDelay)
  }

  /**
   * 数据变化时的同步
   */
  async onDataChange() {
    if (this.isSyncing) {
      return
    }

    try {
      this.isSyncing = true
      await this.uploadData()
      this.lastSyncTime = new Date()
      this.saveSyncStatus()
      this.notifyChange('data_uploaded')
    } catch (error) {
      console.error('数据变化同步失败:', error)
      this.notifyChange('sync_error', error.message)
    } finally {
      this.isSyncing = false
    }
  }

  /**
   * 启动时数据同步检查
   * 比较本地和云端的 lastModified 时间戳，自动决定同步策略
   */
  async checkAndSyncOnStartup() {
    if (this.isSyncing) {
      console.log('正在同步中，跳过重复同步')
      return
    }

    try {
      console.log('开始数据同步检查...')
      this.isSyncing = true

      // 先获取云端数据信息，避免不必要的本地初始化
      const cloudResult = await this.getCloudData()
      if (!cloudResult.success) {
        console.log('获取云端数据失败，跳过同步')
        return
      }

      const cloudTimestamp = normalizeTimestamp(cloudResult.data?.lastModified)

      // 先检查云端数据，再决定本地数据处理策略
      const hasCloudData = cloudResult.data?.hasData

      // 获取本地数据（不触发自动初始化）
      const localData = this.dataManager.getUserDataWithoutInit()
      const hasLocalData = localData && localData.lastModified
      const localTimestamp = hasLocalData ? normalizeTimestamp(localData.lastModified) : 0

      console.log('数据状态 - 本地:', hasLocalData ? '有数据' : '无数据',
                  '云端:', hasCloudData ? '有数据' : '无数据')
      console.log('时间戳比较 - 本地:', localTimestamp, '云端:', cloudTimestamp)

      // 同步策略决策
      if (!hasLocalData && !hasCloudData) {
        // 本地和云端都没有数据，初始化本地数据
        console.log('本地和云端都无数据，初始化本地数据')
        this.dataManager.initializeData()
      } else if (!hasLocalData && hasCloudData) {
        // 本地无数据，云端有数据，下载云端数据
        console.log('本地无数据，下载云端数据')
        await this.downloadAndApplyData()
      } else if (hasLocalData && !hasCloudData) {
        // 本地有数据，云端无数据，上传本地数据
        console.log('云端无数据，上传本地数据')
        await this.uploadData()
      } else {
        // 本地和云端都有数据，比较时间戳
        if (localTimestamp > cloudTimestamp) {
          console.log('本地数据较新，上传到云端')
          await this.uploadData()
        } else if (localTimestamp < cloudTimestamp) {
          console.log('云端数据较新，下载到本地')
          await this.downloadAndApplyData()
        } else {
          console.log('数据时间一致，无需同步')
          wx.showToast({
            title: '数据时间一致，无需同步',
            icon: 'success'
          })
        }
      }

      // 更新同步状态
      this.lastSyncTime = new Date()
      this.saveSyncStatus()
      this.notifyChange('sync_complete')

      console.log('数据同步检查完成')
    } catch (error) {
      console.error('数据同步失败:', error)
      this.notifyChange('sync_error', error.message)
    } finally {
      this.isSyncing = false
    }
  }

  /**
   * 注册数据变化监听器
   */
  registerDataChangeListener() {
    this.dataChangeListener = () => {
      console.log('检测到数据变化，准备上传...')
      // 防抖，避免频繁上传
      this.debounceUpload()
    }

    this.dataManager.addChangeListener(this.dataChangeListener)
  }

  /**
   * 手动同步
   */
  async manualSync() {
    if (this.isSyncing) {
      console.log('同步正在进行中...')
      return
    }

    await this.checkAndSyncOnStartup()
  }

  /**
   * 加载同步状态
   */
  loadSyncStatus() {
    try {
      const status = wx.getStorageSync(this.STORAGE_KEY)
      if (status && status.lastSyncTime) {
        this.lastSyncTime = new Date(status.lastSyncTime)
      }
    } catch (error) {
      console.error('加载同步状态失败:', error)
    }
  }

  /**
   * 保存同步状态
   */
  saveSyncStatus() {
    try {
      const status = {
        lastSyncTime: this.lastSyncTime ? this.lastSyncTime.toISOString() : null
      }
      wx.setStorageSync(this.STORAGE_KEY, status)
    } catch (error) {
      console.error('保存同步状态失败:', error)
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return {
      isInitialized: this.isInitialized,
      isSyncing: this.isSyncing,
      lastSyncTime: this.lastSyncTime,
      lastSyncTimeText: getRelativeTime(this.lastSyncTime),
      hasPermission: true
    }
  }

  /**
   * 添加变化监听器
   */
  addChangeListener(listener) {
    if (typeof listener === 'function') {
      this.changeListeners.push(listener)
    }
  }

  /**
   * 移除变化监听器
   */
  removeChangeListener(listener) {
    const index = this.changeListeners.indexOf(listener)
    if (index > -1) {
      this.changeListeners.splice(index, 1)
    }
  }

  /**
   * 通知变化
   */
  notifyChange(type, data) {
    this.changeListeners.forEach(listener => {
      try {
        listener(type, data)
      } catch (error) {
        console.error('同步状态变化监听器执行失败:', error)
      }
    })
  }

  /**
   * 显示升级提示
   */
  showUpgradeModal(featureDescription = '此功能') {
    wx.showModal({
      title: '功能限制',
      content: `${featureDescription}仅限Pro会员使用\n\n升级到Pro会员即可解锁更多功能！`,
      confirmText: '了解Pro会员',
      cancelText: '暂不升级',
      success: (res) => {
        if (res.confirm) {
          this.showProMembershipInfo()
        }
      }
    })
  }

  /**
   * 显示Pro会员权益信息
   */
  showProMembershipInfo() {
    const content = `Pro会员专享功能：
• 数据同步功能
• 高级数据分析
• 历史数据管理
• 自定义主题

使用限制提升：
• 工作履历：免费版5个 → Pro版无限制
• 每日时间段：免费版10个 → Pro版无限制
• 数据保留：免费版30天 → Pro版永久保留

敬请期待会员功能上线！`

    wx.showModal({
      title: 'Pro会员权益',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    })
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.dataManager && this.dataChangeListener) {
      this.dataManager.removeChangeListener(this.dataChangeListener)
      this.dataChangeListener = null
    }

    this.changeListeners = []
    console.log('数据同步管理器资源清理完成')
  }
}

// 导出单例实例
let syncManagerInstance = null

function getSyncManager() {
  if (!syncManagerInstance) {
    syncManagerInstance = new SyncManager()
  }
  return syncManagerInstance
}

module.exports = {
  SyncManager,
  getSyncManager
}
