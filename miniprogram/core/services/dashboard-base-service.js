/**
 * 仪表盘基础服务
 * 提供仪表盘组件的共用方法和数据处理
 */

const { getWorkHistoryService } = require('./work-history-service.js')
const { getTimeSegmentService } = require('./time-segment-service.js')

/**
 * 仪表盘基础服务类
 */
class DashboardBaseService {
  constructor() {
    this.workHistoryService = getWorkHistoryService()
    this.timeSegmentService = getTimeSegmentService()

    // 延迟初始化数据管理器，避免循环依赖
    this._dataManager = null
  }

  /**
   * 获取数据管理器实例（延迟初始化）
   * @returns {DataManager} 数据管理器实例
   */
  get dataManager() {
    if (!this._dataManager) {
      this._dataManager = getApp().getDataManager()
    }
    return this._dataManager
  }

  /**
   * 检查是否有工作履历
   * @returns {boolean} 是否有工作履历
   */
  hasWorkHistory() {
    return this.workHistoryService.hasWorkHistory()
  }

  /**
   * 获取当前工作履历
   * @returns {Object|null} 当前工作履历
   */
  getCurrentWork() {
    if (!this.hasWorkHistory()) {
      return null
    }
    return this.workHistoryService.ensureCurrentWork()
  }

  /**
   * 获取用户设置
   * @returns {Object} 用户设置
   */
  getUserSettings() {
    try {
      if (this.dataManager) {
        return this.dataManager.getSettings()
      }
    } catch (error) {
      console.error('获取用户设置失败:', error)
    }

    // 返回默认设置
    return {
      income: {
        currencySymbol: '¥',
        decimalPlaces: 2
      },
      display: {
        privacyMask: {}
      }
    }
  }

  /**
   * 获取仪表盘配置
   * @param {string} dashboardId 仪表盘ID
   * @returns {Object} 仪表盘配置
   */
  getDashboardConfig(dashboardId) {
    try {
      if (this.dataManager) {
        return this.dataManager.getDashboardConfig(dashboardId)
      }
    } catch (error) {
      console.error('获取仪表盘配置失败:', error)
    }

    // 返回默认配置
    return {
      showCurrentWork: true
    }
  }

  /**
   * 获取货币符号
   * @returns {string} 货币符号
   */
  getCurrencySymbol() {
    const settings = this.getUserSettings()
    return settings.income?.currencySymbol || '¥'
  }

  /**
   * 获取小数位数
   * @param {string} dashboardId 仪表盘ID（可选，如果不提供则返回默认值3）
   * @returns {number} 小数位数
   */
  getDecimalPlaces(dashboardId) {
    if (dashboardId) {
      // 如果提供了仪表盘ID，从仪表盘配置中获取
      try {
        const { DashboardService } = require('./dashboard-service.js')
        const dashboardService = new DashboardService()
        const dashboardConfig = dashboardService.getDashboardSettings(dashboardId)
        return dashboardConfig.incomeDecimalPlaces !== undefined ? dashboardConfig.incomeDecimalPlaces : 3
      } catch (error) {
        console.error('获取仪表盘小数位数失败:', error)
      }
    }

    // 如果没有提供仪表盘ID，返回默认值3位小数
    console.warn('[DEBUG] 没有提供仪表盘ID，返回默认值3')
    return 3
  }

  /**
   * 格式化金额
   * @param {number} amount 金额
   * @param {string} dashboardId 仪表盘ID（可选）
   * @returns {string} 格式化后的金额
   */
  formatAmount(amount, dashboardId) {
    const decimalPlaces = this.getDecimalPlaces(dashboardId)
    return (amount || 0).toFixed(decimalPlaces)
  }

  /**
   * 获取今日数据
   * @param {Date} date 日期
   * @param {string} workId 工作ID
   * @returns {Object} 今日数据
   */
  getTodayData(date, workId) {
    if (!workId) {
      return {
        segments: [],
        dailyIncome: 0,
        workMinutes: 0,
        restMinutes: 0
      }
    }

    const dayData = this.timeSegmentService.getDayData(date, workId)
    
    // 计算工作时间和休息时间
    let workMinutes = 0
    let restMinutes = 0
    
    dayData.segments.forEach(segment => {
      const duration = segment.end - segment.start
      if (segment.type === 'rest') {
        restMinutes += duration
      } else {
        workMinutes += duration
      }
    })
    
    return {
      segments: dayData.segments,
      fishes: dayData.fishes || [], // 摸鱼数据
      dailyIncome: dayData.dailyIncome || 0,
      workMinutes: workMinutes,
      restMinutes: restMinutes
    }
  }

  /**
   * 获取月度统计
   * @param {Date} date 日期
   * @param {string} workId 工作ID
   * @returns {Object} 月度统计
   */
  getMonthlyStats(date, workId) {
    if (!workId) {
      return {
        totalIncome: 0,
        totalWorkMinutes: 0,
        averageHourlyRate: 0
      }
    }

    const currentMonth = new Date(date.getFullYear(), date.getMonth(), 1)
    const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1)
    
    return this.timeSegmentService.calculateIncomeStatistics(
      [currentMonth, nextMonth], 
      workId
    )
  }

  /**
   * 获取年度统计
   * @param {Date} date 日期
   * @param {string} workId 工作ID
   * @returns {Object} 年度统计
   */
  getYearlyStats(date, workId) {
    if (!workId) {
      return {
        totalIncome: 0,
        totalWorkMinutes: 0
      }
    }

    const currentYear = new Date(date.getFullYear(), 0, 1)
    const nextYear = new Date(date.getFullYear() + 1, 0, 1)
    
    return this.timeSegmentService.calculateIncomeStatistics(
      [currentYear, nextYear], 
      workId
    )
  }

  /**
   * 计算时薪
   * @param {number} totalIncome 总收入
   * @param {number} totalMinutes 总工作分钟数
   * @returns {number} 时薪
   */
  calculateHourlyRate(totalIncome, totalMinutes) {
    if (totalMinutes <= 0) {
      return 0
    }
    return totalIncome / (totalMinutes / 60)
  }

  /**
   * 获取当前工作状态
   * @param {Date} now 当前时间
   * @param {Array} segments 时间段数组
   * @returns {Object} 工作状态
   */
  getCurrentWorkStatus(now, segments) {
    let currentStatus = {
      type: 'off',
      icon: '😴',
      text: '未开始工作',
      countdown: null,
      nextStatusText: '',
      isWorking: false
    }
    
    if (!segments || segments.length === 0) {
      return currentStatus
    }

    // 查找当前时间所在的时间段
    for (const segment of segments) {
      if (now >= segment.start && now < segment.end) {
        const remainingTime = Math.floor((segment.end - now) / 1000)
        const remainingHours = Math.floor(remainingTime / 3600)
        const remainingMinutes = Math.floor((remainingTime % 3600) / 60)
        const remainingSeconds = remainingTime % 60

        // 根据时长决定显示格式
        const countdown = remainingHours > 0
          ? `${remainingHours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
          : `${remainingMinutes}:${remainingSeconds.toString().padStart(2, '0')}`
        
        if (segment.type === 'work') {
          currentStatus = {
            type: 'working',
            icon: '💼',
            text: '正在工作',
            countdown: countdown,
            nextStatusText: '距离下一段',
            isWorking: true
          }
        } else if (segment.type === 'rest') {
          currentStatus = {
            type: 'resting',
            icon: '☕',
            text: '正在休息',
            countdown: countdown,
            nextStatusText: '距离下一段',
            isWorking: false
          }
        } else if (segment.type === 'overtime') {
          currentStatus = {
            type: 'overtime',
            icon: '🔥',
            text: '正在加班',
            countdown: countdown,
            nextStatusText: '距离下一段',
            isWorking: true
          }
        }
        break
      }
    }
    
    // 如果当前时间不在任何时间段内，区分未上班和已下班
    if (currentStatus.type === 'off') {
      const currentMinutes = now.getHours() * 60 + now.getMinutes()
      const futureSegments = segments.filter(segment => segment.start > currentMinutes)
      const pastSegments = segments.filter(segment => segment.end < currentMinutes)

      if (futureSegments.length > 0) {
        // 还有未来的时间段，说明是未上班
        const nextSegment = futureSegments[0]
        const timeToNext = (nextSegment.start - currentMinutes) * 60 // 转换为秒
        const hours = Math.floor(timeToNext / 3600)
        const minutes = Math.floor((timeToNext % 3600) / 60)
        const seconds = Math.floor(timeToNext % 60)

        // 根据时长决定显示格式
        const countdown = hours > 0
          ? `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
          : `${minutes}:${seconds.toString().padStart(2, '0')}`

        const nextSegmentTypeName = this.getSegmentTypeName(nextSegment.type)

        currentStatus = {
          type: 'before_work',
          icon: '🌅',
          text: '未上班',
          countdown: countdown,
          nextStatusText: `距离${nextSegmentTypeName}`,
          isWorking: false
        }
      } else if (pastSegments.length > 0) {
        // 所有时间段都已过去，说明是已下班
        currentStatus = {
          type: 'after_work',
          icon: '🌙',
          text: '已下班',
          countdown: null,
          nextStatusText: '',
          isWorking: false
        }
      }
    }
    
    return currentStatus
  }

  /**
   * 获取时间段类型的中文名称
   * @param {string} type - 时间段类型
   * @returns {string} 中文名称
   */
  getSegmentTypeName(type) {
    switch (type) {
      case 'work':
        return '工作'
      case 'rest':
        return '休息'
      case 'overtime':
        return '加班'
      default:
        return '工作'
    }
  }

  /**
   * 计算工作进度百分比
   * @param {Date} now 当前时间
   * @param {number} startHour 开始小时 (默认9)
   * @param {number} endHour 结束小时 (默认18)
   * @returns {number} 进度百分比 (0-100)
   */
  calculateWorkProgress(now, startHour = 9, endHour = 18) {
    const startOfWork = new Date(now)
    startOfWork.setHours(startHour, 0, 0, 0)
    
    const endOfWork = new Date(now)
    endOfWork.setHours(endHour, 0, 0, 0)
    
    if (now >= startOfWork && now <= endOfWork) {
      const totalWorkTime = endOfWork - startOfWork
      const elapsedTime = now - startOfWork
      const progress = Math.round((elapsedTime / totalWorkTime) * 100)
      
      return Math.min(100, Math.max(0, progress))
    }
    
    return now < startOfWork ? 0 : 100
  }

  /**
   * 计算下班倒计时
   * @param {Date} now 当前时间
   * @param {number} endHour 下班小时 (默认18)
   * @returns {Object} 倒计时对象
   */
  calculateOffWorkCountdown(now, endHour = 18) {
    const endOfWork = new Date(now)
    endOfWork.setHours(endHour, 0, 0, 0)
    
    // 如果已经过了下班时间，设置为明天的下班时间
    if (now >= endOfWork) {
      endOfWork.setDate(endOfWork.getDate() + 1)
    }
    
    const timeDiff = endOfWork - now
    const hours = Math.floor(timeDiff / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)
    
    return {
      hours: hours.toString().padStart(2, '0'),
      minutes: minutes.toString().padStart(2, '0'),
      seconds: seconds.toString().padStart(2, '0'),
      text: '距离下班还有'
    }
  }

  /**
   * 生成脱敏文本
   * @param {string} text 原始文本
   * @returns {string} 脱敏文本
   */
  generateMaskedText(text) {
    if (!text || text === '0.00') {
      return '***.**'
    }
    
    const parts = text.split('.')
    const integerPart = parts[0]
    const decimalPart = parts[1] || '00'
    
    const maskedInteger = '*'.repeat(Math.max(1, integerPart.length))
    const maskedDecimal = '*'.repeat(decimalPart.length)
    
    return `${maskedInteger}.${maskedDecimal}`
  }
}

// 导出单例实例
let dashboardBaseServiceInstance = null

function getDashboardBaseService() {
  if (!dashboardBaseServiceInstance) {
    dashboardBaseServiceInstance = new DashboardBaseService()
  }
  return dashboardBaseServiceInstance
}

module.exports = {
  DashboardBaseService,
  getDashboardBaseService
}
