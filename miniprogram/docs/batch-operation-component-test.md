# 批量操作组件测试文档

## 测试目标
验证批量操作组件的完整功能，包括导入模式和批量复制模式的所有特性。

## 测试环境
- 微信开发者工具
- 真机测试（推荐）

## 测试用例

### 1. 基础功能测试

#### 1.1 模态框打开/关闭
- [ ] 点击"批量复制"按钮，模态框正常打开（复制模式）
- [ ] 点击"导入安排"按钮，模态框正常打开（导入模式）
- [ ] 点击关闭按钮，模态框正常关闭
- [ ] 点击遮罩层，模态框正常关闭
- [ ] 模态框动画效果正常

### 2. 导入模式测试

#### 2.1 模板列表显示
- [ ] 正确显示所有有工作安排的日期作为模板
- [ ] 模板显示日期、状态、时间段数量、收入信息
- [ ] 如果没有可导入的模板，显示空状态提示

#### 2.2 模板选择和导入
- [ ] 点击模板项，正确选中（高亮显示）
- [ ] 点击"确定导入"按钮，成功导入工作安排
- [ ] 验证导入后的数据正确性（时间段、状态、收入）
- [ ] 未选择模板时，"确定导入"按钮为禁用状态

### 3. 批量复制模式测试

#### 3.1 步骤1：源日期选择
- [ ] 默认显示"选择源日期"界面
- [ ] 日历正确显示当前月份
- [ ] 有工作安排的日期显示数据指示器
- [ ] 点击有数据的日期，正确选中并显示预览
- [ ] 点击无数据的日期，显示提示信息
- [ ] 工作安排预览显示完整信息（日期、收入、时间段详情）

#### 3.2 日历导航功能
- [ ] 点击上一年/下一年按钮，正确切换年份
- [ ] 点击上一月/下一月按钮，正确切换月份
- [ ] 切换后日历数据正确更新
- [ ] 选中状态在月份切换后保持

#### 3.3 步骤2：目标日期选择
- [ ] 选择源日期后，点击"下一步"进入目标日期选择
- [ ] 界面正确显示"选择目标日期"
- [ ] 源日期在日历中显示为橙色（已选中状态）
- [ ] 点击"上一步"可以返回源日期选择

#### 3.4 目标日期多选功能
- [ ] 点击日期可以选中/取消选中
- [ ] 选中的日期显示为绿色
- [ ] 不能选择源日期作为目标日期
- [ ] 不能选择任职范围外的日期
- [ ] 选择统计正确显示已选择的日期数量

#### 3.5 复制选项设置
- [ ] "复制状态"开关正常工作
- [ ] "复制收入"开关正常工作
- [ ] 选项状态正确保存

#### 3.6 快速选择功能
- [ ] 点击"选择全部工作日"，正确选择当月所有工作日
- [ ] 工作日数量统计正确
- [ ] 排除源日期和任职范围外的日期
- [ ] 点击"清空选择"，清除所有已选择的日期
- [ ] 快速选择后显示成功提示

### 4. 高级功能测试

#### 4.1 工作日识别
- [ ] 正确识别周一到周五为工作日
- [ ] 如果有节假日数据，正确处理节假日和调休
- [ ] 工作日在日历中有特殊标识

#### 4.2 任职范围检查
- [ ] 任职范围外的日期显示为灰色
- [ ] 不能选择任职范围外的日期作为目标
- [ ] 点击任职范围外日期显示提示信息

#### 4.3 数据指示器
- [ ] 有工作安排的日期显示圆形指示器
- [ ] 指示器颜色与状态配置一致
- [ ] 指示器显示时间段数量

### 5. 批量复制执行测试

#### 5.1 复制操作
- [ ] 选择源日期和目标日期后，点击"确定复制"
- [ ] 成功复制工作安排到所有目标日期
- [ ] 根据复制选项正确处理状态和收入
- [ ] 显示成功提示信息

#### 5.2 数据验证
- [ ] 复制后的时间段信息正确
- [ ] 如果选择复制状态，状态信息正确
- [ ] 如果选择复制收入，收入信息正确
- [ ] 如果不复制某项，该项保持原值或默认值

### 6. 边界情况测试

#### 6.1 无数据情况
- [ ] 没有任何工作安排时，导入模式显示空状态
- [ ] 当前月份没有工作安排时，复制模式正常工作
- [ ] 没有工作日时，快速选择功能正常

#### 6.2 选择限制
- [ ] 未选择源日期时，"下一步"按钮禁用
- [ ] 未选择目标日期时，"确定复制"按钮禁用
- [ ] 未选择模板时，"确定导入"按钮禁用

#### 6.3 错误处理
- [ ] 网络错误时显示合适提示
- [ ] 数据加载失败时的处理
- [ ] 操作失败时的错误提示

### 7. 性能测试

#### 7.1 响应速度
- [ ] 模态框打开速度 < 500ms
- [ ] 日历切换响应 < 300ms
- [ ] 日期选择响应 < 200ms
- [ ] 批量复制完成时间合理

#### 7.2 大数据量测试
- [ ] 选择大量目标日期（如整月）时性能正常
- [ ] 复制大量数据时不卡顿
- [ ] 内存使用合理

### 8. 用户体验测试

#### 8.1 界面友好性
- [ ] 步骤指示清晰
- [ ] 操作提示明确
- [ ] 视觉反馈及时
- [ ] 错误提示友好

#### 8.2 操作便利性
- [ ] 快速选择功能实用
- [ ] 步骤导航方便
- [ ] 选择状态清晰可见

## 关键验证点

### ✅ 复杂状态管理
验证组件能正确处理：
1. 两种操作模式的切换
2. 多步骤流程的状态转换
3. 多种日期选择状态的管理

### ✅ 服务集成
验证组件能正确：
1. 通过页面实例访问 timeSegmentService
2. 获取和处理工作安排数据
3. 调用节假日管理器识别工作日

### ✅ 数据处理
验证组件能正确：
1. 生成和更新日历数据
2. 处理复杂的日期计算
3. 执行批量数据操作

## 测试通过标准
- [ ] 所有基础功能正常
- [ ] 两种操作模式都能正确工作
- [ ] 复杂的多步骤流程顺畅
- [ ] 高级功能（快速选择、复制选项）正常
- [ ] 边界情况处理合理
- [ ] 无明显性能问题
- [ ] 用户体验良好
- [ ] 无控制台错误

## 测试报告模板
```
测试时间：____
测试环境：____
测试结果：通过/失败
失败项目：____
性能表现：____
用户体验：____
备注：____
```
