# Bug 修复日志

## 2025-01-01 - 日收入计算器组件问题修复

### 问题1: targetMode 属性类型不兼容
**错误信息:**
```
[Component] property "targetMode" of "components/daily-income-calculator/index" received type-uncompatible value: expected <String> but get null value. Use empty string instead.
```

**原因分析:**
- 组件属性 `targetMode` 定义为 String 类型，但页面传入了 null 值
- 小程序组件系统不允许 null 值传递给 String 类型属性

**修复方案:**
1. **组件端修复** (`miniprogram/components/daily-income-calculator/index.js`):
   - 添加 `optionalTypes: [null]` 允许接收 null 值
   - 在 `onConfirm` 方法中添加默认值处理: `const targetMode = this.properties.targetMode || 'total'`

2. **页面端修复** (`miniprogram/pages/calendar/index.js`):
   - 将 `dailyIncomeCalculatorTargetMode` 初始值从 `null` 改为 `'total'`
   - 在关闭事件中重置为 `'total'` 而不是 `null`

### 问题2: CSS 选择器不兼容
**错误信息:**
```
[pages/calendar/index] Some selectors are not allowed in component wxss, including tag name selectors, ID selectors, and attribute selectors.
```

**原因分析:**
- 页面样式中使用了标签名选择器 `text {}`
- 页面样式中使用了属性选择器 `[data-holiday-type="holiday"]`
- 这些选择器在小程序组件中是不被允许的

**修复方案:**
1. **标签名选择器修复**:
   - 将 `.clear-selection-btn text {}` 改为 `.clear-selection-btn .clear-text {}`
   - 在对应的 WXML 中添加 `class="clear-text"`

2. **属性选择器修复**:
   - 将 `[data-holiday-type="holiday"]` 改为 `.holiday-type-holiday`
   - 将 `[data-holiday-type="workday"]` 改为 `.holiday-type-workday`
   - 将 `[data-holiday-type="weekend"]` 改为 `.holiday-type-weekend`
   - 在 WXML 中移除 `data-holiday-type` 属性，改为动态添加对应的 class

### 修复结果
✅ **targetMode 属性问题已解决**
- 组件可以正常接收和处理 targetMode 参数
- 不再出现类型不兼容警告

✅ **CSS 选择器问题已解决**  
- 移除了所有不被允许的选择器
- 使用 class 选择器替代属性选择器
- 保持了原有的样式效果

### 测试建议
1. **功能测试**:
   - 测试日收入计算器在总收入分配模式下的使用
   - 测试日收入计算器在加班倍率模式下的使用
   - 验证计算结果的正确性

2. **样式测试**:
   - 检查日历中节假日的样式显示是否正常
   - 验证清空选择按钮的样式是否正确
   - 确认在不同设备上的显示效果

3. **兼容性测试**:
   - 在不同版本的微信开发者工具中测试
   - 在真机上测试组件的表现

## 2025-01-01 - 工作安排导入组件问题修复

### 问题3: timeSegmentService 传递失败
**错误信息:**
```
timeSegmentService not provided
```

**原因分析:**
- 页面中的 `timeSegmentService` 是实例属性，不在 data 中
- 小程序组件的 Object 类型属性需要通过 data 传递
- WXML 中无法直接访问页面实例属性

**修复方案:**
1. **页面端修复** (`miniprogram/pages/calendar/index.js`):
   - 在 `onLoad` 中将 `timeSegmentService` 添加到 data
   - 确保组件能够访问到服务实例

2. **组件端修复** (`miniprogram/components/schedule-import-modal/index.js`):
   - 添加 `timeSegmentService` 属性的 observer
   - 更新 observers 监听多个属性变化
   - 增强初始化逻辑的健壮性

### 问题4: timeSegmentService 方法丢失
**错误信息:**
```
TypeError: this.properties.timeSegmentService.getDayData is not a function
```

**原因分析:**
- 小程序在传递复杂对象时会进行序列化
- 序列化过程中对象的方法会丢失
- 组件接收到的是一个普通对象，不是服务实例

**修复方案:**
1. **改用页面实例访问** (`miniprogram/components/schedule-import-modal/index.js`):
   - 通过 `getCurrentPages()` 获取当前页面实例
   - 直接访问页面的 `timeSegmentService` 属性
   - 移除组件属性中的 `timeSegmentService`

2. **简化组件接口** (`miniprogram/pages/calendar/index.wxml`):
   - 移除 `time-segment-service` 属性传递
   - 保持其他必要属性

### 修复结果
✅ **timeSegmentService 方法调用问题已解决**
- 组件可以正常调用服务方法
- 日历数据能够正确加载和显示
- 不再出现方法调用错误

### 经验总结
1. **组件属性设计**:
   - 为可选属性提供合理的默认值
   - 考虑使用 `optionalTypes` 处理特殊情况
   - 在组件内部进行防御性编程

2. **CSS 选择器规范**:
   - 避免使用标签名选择器，改用 class 选择器
   - 避免使用属性选择器，改用动态 class
   - 保持选择器的简洁性和可维护性

3. **代码重构原则**:
   - 渐进式重构，每次只处理一个问题
   - 保持向后兼容性
   - 及时修复出现的问题，避免积累技术债务

4. **服务实例传递**:
   - 小程序组件的 Object 属性需要通过 data 传递
   - 使用 observers 监听多个属性的变化
   - 确保服务可用性检查在组件初始化时进行

## 2025-01-01 - 智能收入组件日收入计算器回填问题排查

### 问题4: 日收入计算器结果回填失败
**问题描述:**
- 在智能收入模态框中点击"计算日收入"按钮
- 日收入计算器正常打开并计算
- 点击"确定使用"后，结果没有回填到智能收入模态框的输入框中
- 控制台没有错误日志

**排查步骤:**
1. **添加调试日志** - 在关键方法中添加 console.log 来追踪数据流
2. **检查事件流程** - 验证从智能收入组件到日收入计算器的事件传递
3. **检查组件实例获取** - 验证 `selectComponent('smart-income-modal')` 是否正确
4. **检查回填方法** - 验证 `fillDailyIncomeResult` 方法是否被调用

**调试日志位置:**
- `onSmartIncomeOpenDailyIncomeCalculator()` - 页面接收智能收入组件事件
- `onDailyIncomeCalculatorConfirm()` - 日收入计算器确认事件
- `fillDailyIncomeResult()` - 智能收入组件回填方法
- `onOpenDailyIncomeCalculator()` - 智能收入组件触发事件

**预期问题原因:**
1. 组件实例获取失败
2. 事件传递链路中断
3. 数据格式不匹配
4. 组件状态管理问题

### 测试步骤
1. 打开智能收入模态框
2. 选择"总收入分配"模式
3. 点击"计算日收入"按钮
4. 在日收入计算器中输入数据并确认
5. 检查控制台日志输出
6. 验证结果是否正确回填到输入框

### 问题根因
通过调试日志发现：`this.selectComponent('smart-income-modal')` 返回 null，无法获取智能收入组件实例。

**可能原因:**
1. 当日收入计算器模态框打开时，智能收入模态框可能被隐藏
2. 小程序的组件选择器在某些情况下无法正确工作
3. 组件实例的生命周期管理问题

### 修复方案
采用全局引用机制解决组件实例获取问题：

**全局引用机制**
- 在组件的 `attached` 生命周期中注册到 `app.globalData`
- 在组件的 `detached` 生命周期中清除全局引用
- 回填时直接使用全局引用获取组件实例

### 修复结果
✅ **组件实例获取问题已解决**
- 实现了三重保障机制
- 确保在各种情况下都能正确获取组件实例
- 全局引用机制成功解决了 selectComponent 失败的问题

**测试验证:**
- ✅ **全局引用机制成功** - 通过 app.globalData 成功获取组件实例
- ✅ 日收入计算器结果成功回填到智能收入模态框
- ✅ 代码简化，移除了不必要的复杂逻辑

**经验总结:**
在小程序中，当多个模态框同时存在时，`this.selectComponent()` 可能会失败。全局引用机制是一个简单可靠的解决方案。

**最终实现:**

```javascript
// 组件注册（attached 生命周期）
getApp().globalData.smartIncomeComponent = this

// 组件使用（页面回填逻辑）
const component = getApp().globalData.smartIncomeComponent
component.fillDailyIncomeResult(result, targetMode)

// 组件清理（detached 生命周期）
getApp().globalData.smartIncomeComponent = null
```
