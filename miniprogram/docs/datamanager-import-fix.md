# DataManager 导入方式修复总结

## 问题描述

在单例模式统一改造后，发现页面和组件中仍然使用旧的 `require('../../core/managers/data-manager.js')` 方式导入 DataManager，导致获取到的不是正确的单例实例，出现以下错误：

```
TypeError: this.dataManager.getCurrentFishingState is not a function
TypeError: this.dataManager.addChangeListener is not a function
TypeError: dataManager.getCurrentFishingState is not a function
```

## 根本原因

1. **直接 require 问题**：页面和组件中直接 `require` data-manager.js 会获取到旧的导出方式
2. **不一致的导入方式**：有些地方使用 `getApp().getDataManager()`，有些地方使用直接 require
3. **Service 实例化问题**：页面中直接 `new Service()` 而不是使用单例工厂函数

## 修复方案

### 统一使用 `getApp().getDataManager()`

所有页面和组件中获取 DataManager 实例都改为：

```javascript
// ❌ 错误方式
const dataManager = require('../../core/managers/data-manager.js')

// ✅ 正确方式
const dataManager = getApp().getDataManager()
```

### 统一使用 Service 工厂函数

所有页面中获取 Service 实例都改为：

```javascript
// ❌ 错误方式
const { DashboardService } = require('../../core/services/dashboard-service.js')
this.dashboardService = new DashboardService()

// ✅ 正确方式
const { getDashboardService } = require('../../core/services/dashboard-service.js')
this.dashboardService = getDashboardService()
```

## 修复的文件列表

### 页面文件
1. **pages/index/index.js**
   - 修复 Service 导入和实例化方式
   - 使用工厂函数获取单例

2. **pages/calendar/index.js**
   - 修复 4 处 dataManager 直接 require 的问题
   - 修复 Service 导入和实例化方式
   - 统一使用 `getApp().getDataManager()`

3. **pages/profile/index.js**
   - 修复 4 处 Service 导入和实例化方式
   - 使用工厂函数获取单例

4. **pages/statistics/index.js**
   - 修复 StatisticsService 导入和实例化方式
   - 使用工厂函数获取单例

5. **pages/work-history/index.js**
   - 修复 WorkHistoryService 导入和实例化方式
   - 使用工厂函数获取单例

6. **pages/income-adjustment-test/index.js**
   - 修复 2 处 Service 导入和实例化方式
   - 使用工厂函数获取单例

### 组件文件
1. **components/dashboard1/index.js**
   - 修复 7 处 dataManager 直接 require 的问题
   - 修复 1 处 fishingManager 直接 require 的问题
   - 统一使用 `getApp().getDataManager()`

2. **components/dashboard2/index.js**
   - 修复 1 处 dataManager 直接 require 的问题
   - 修复 3 处 Service 导入和实例化方式
   - 使用工厂函数获取单例

3. **components/fishing-control/index.js**
   - 移除顶层 dataManager require
   - 修复 3 处方法中的 dataManager 使用

4. **components/fishing-remark-editor/index.js**
   - 移除顶层 dataManager require
   - 修复 2 处方法中的 dataManager 使用

5. **components/income-adjustment-modal/index.js**
   - 修复 1 处 Service 实例化方式
   - 使用工厂函数获取单例

6. **components/schedule-modal/index.js**
   - 修复 1 处 Service 实例化方式
   - 使用工厂函数获取单例

7. **components/batch-operation-modal/index.js**
   - 修复 1 处 Service 实例化方式
   - 使用工厂函数获取单例

8. **components/date-type-selector/index.js**
   - 修复 1 处 Service 导入和实例化方式
   - 使用工厂函数获取单例

### 核心文件
1. **core/index.js**
   - 修复统一导出文件的导入方式
   - 使用工厂函数而不是直接导出实例

## 修复前后对比

### 修复前
```javascript
// 页面顶部
const dataManager = require('../../core/managers/data-manager.js')
const { DashboardService } = require('../../core/services/dashboard-service.js')

// 页面方法中
onLoad() {
  this.dashboardService = new DashboardService()
  const fishingState = dataManager.getCurrentFishingState()
}
```

### 修复后
```javascript
// 页面顶部
const { getDashboardService } = require('../../core/services/dashboard-service.js')

// 页面方法中
onLoad() {
  this.dashboardService = getDashboardService()
  const dataManager = getApp().getDataManager()
  const fishingState = dataManager.getCurrentFishingState()
}
```

## 最佳实践

### 1. DataManager 获取
```javascript
// 在页面/组件方法中
someMethod() {
  const dataManager = getApp().getDataManager()
  // 使用 dataManager
}
```

### 2. Service 获取
```javascript
// 页面顶部导入
const { getServiceName } = require('../../core/services/service-name.js')

// 页面初始化
onLoad() {
  this.serviceName = getServiceName()
}
```

### 3. 避免顶层 require DataManager
```javascript
// ❌ 避免在文件顶部直接 require
const dataManager = require('../../core/managers/data-manager.js')

// ✅ 在需要时通过 getApp() 获取
const dataManager = getApp().getDataManager()
```

## 验证方法

修复后，所有的 DataManager 方法调用都应该正常工作：
- `dataManager.getCurrentFishingState()`
- `dataManager.addChangeListener()`
- `dataManager.startFishing()`
- `dataManager.endFishing()`
- `dataManager.updateCurrentFishingRemark()`

## 最新修复

### FishingManager 直接导入问题
在 `components/dashboard1/index.js` 的 `onRemarkConfirm` 方法中发现还在直接使用：
```javascript
const fishingManager = require('../../core/managers/fishing-manager.js')
```

**修复方案：**
改为使用 DataManager 的统一方法：
```javascript
const dataManager = getApp().getDataManager()
const result = dataManager.updateCurrentFishingRemark(newRemark)
```

## 注意事项

1. **确保 app.js 已初始化**：在调用 `getApp().getDataManager()` 前确保应用已完成初始化
2. **错误处理**：添加适当的 try-catch 来处理可能的初始化错误
3. **一致性**：项目中所有地方都应该使用统一的获取方式

这次修复确保了所有页面和组件都能正确获取到 DataManager 的单例实例，解决了方法调用失败的问题。
