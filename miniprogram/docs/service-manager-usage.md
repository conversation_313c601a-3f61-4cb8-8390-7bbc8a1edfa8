# 统一服务管理器使用指南

## 概述

项目现在已经完成了单例模式的统一改造，所有的 Service 和 Manager 都使用工厂函数模式实现单例。通过 app.js 的 `getServices()` 和 `getManagers()` 方法可以一次性获取所有服务实例。

## 单例模式统一实现

### 统一的单例模式（工厂函数模式）

所有的 Service 和 Manager 文件现在都使用以下统一格式：

```javascript
// 服务/管理器文件末尾
let serviceInstance = null

function getServiceName() {
  if (!serviceInstance) {
    serviceInstance = new ServiceName()
  }
  return serviceInstance
}

module.exports = {
  ServiceName,
  getServiceName
}
```

### 优势
1. **延迟初始化**：只有在需要时才创建实例
2. **更好的测试支持**：可以重置实例进行单元测试
3. **更灵活的依赖注入**：可以在创建时传入依赖
4. **统一的接口**：所有服务都通过 app.js 的统一方法获取
5. **真正的单例**：确保全局只有一个实例

## 使用方式

### 方式1：获取单个服务（推荐）

```javascript
// 在页面或组件中
const app = getApp()

// 获取单个服务
const timeSegmentService = app.getTimeSegmentService()
const dataManager = app.getDataManager()
const fishingManager = app.getFishingManager()
```

### 方式2：获取所有服务

```javascript
// 在页面或组件中
const app = getApp()

// 获取所有服务
const services = app.getServices()
const managers = app.getManagers()

// 使用服务
services.timeSegmentService.getDayData(date)
managers.dataManager.getCurrentWork()
```

### 方式3：直接导入（不推荐，仅作备用）

```javascript
// 仅在无法访问 getApp() 时使用
const { getTimeSegmentService } = require('../../core/services/time-segment-service.js')
const timeSegmentService = getTimeSegmentService()

const { getFishingManager } = require('../../core/managers/fishing-manager.js')
const fishingManager = getFishingManager()
```

## 服务列表

### 可用的服务
- `timeSegmentService` - 时间段服务
- `workHistoryService` - 工作履历服务
- `statisticsService` - 统计服务
- `incomeAdjustmentService` - 收入调整服务
- `dataImportExportService` - 数据导入导出服务
- `dashboardService` - 仪表盘服务
- `dashboardBaseService` - 仪表盘基础服务

### 可用的管理器
- `dataManager` - 数据管理器
- `userManager` - 用户管理器
- `syncManager` - 同步管理器
- `holidayManager` - 节假日管理器
- `storageManager` - 存储管理器
- `workManager` - 工作管理器
- `settingsManager` - 设置管理器
- `timeTrackingManager` - 时间追踪管理器
- `fishingManager` - 摸鱼管理器

## 最佳实践

### 在组件中使用

```javascript
// components/example/index.js
Component({
  lifetimes: {
    attached() {
      // 直接获取服务
      const app = getApp()
      this.timeSegmentService = app.getTimeSegmentService()
      this.dataManager = app.getDataManager()
    }
  },

  methods: {
    someMethod() {
      // 使用服务
      const data = this.timeSegmentService.getDayData(new Date())
    }
  }
})
```

### 在页面中使用

```javascript
// pages/example/index.js
Page({
  onLoad() {
    // 直接获取服务
    const app = getApp()
    this.dataManager = app.getDataManager()
    this.timeSegmentService = app.getTimeSegmentService()
    this.fishingManager = app.getFishingManager()
  },

  someMethod() {
    // 使用服务
    const currentWork = this.dataManager.getCurrentWork()
    const dayData = this.timeSegmentService.getDayData(new Date())
  }
})
```

## 测试支持

```javascript
// 在测试中重置服务实例
const app = getApp()
app._resetServices()

// 重新初始化服务
const services = app.getServices()
```

## 注意事项

1. **优先使用 app.js 的方法**：这样可以确保获取到正确的单例实例
2. **提供备用方案**：在 app.js 方法不可用时，直接导入和实例化
3. **避免重复实例化**：使用单例模式确保全局只有一个实例
4. **错误处理**：添加适当的错误处理和日志
5. **性能考虑**：服务实例会被缓存，避免重复创建

## 迁移指南

### 从旧的导入方式迁移

```javascript
// 旧方式
const fishingManager = require('../../core/managers/fishing-manager.js')

// 新方式
const app = getApp()
const fishingManager = app.getFishingManager()

// 或者
const { getFishingManager } = require('../../core/managers/fishing-manager.js')
const fishingManager = getFishingManager()
```

这种统一的服务管理方式让代码更加一致、可维护，并且提供了更好的测试支持。
