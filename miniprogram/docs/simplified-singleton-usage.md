# 简化的单例使用方案

## 概述

经过重新思考，我们采用了更简洁的解决方案：**直接使用 `getApp().get***()` 方法**，无需在页面和组件中导入任何 Service 或 Manager 文件。

## 核心理念

既然 `app.js` 已经提供了所有的 `get***Manager()` 和 `get***Service()` 方法，那么在页面和组件中应该直接使用这些方法，而不是重复导入。

## 修复前后对比

### 修复前（复杂方案）
```javascript
// 页面顶部需要导入
const { getDashboardService } = require('../../core/services/dashboard-service.js')
const { getWorkHistoryService } = require('../../core/services/work-history-service.js')
const { getDataManager } = require('../../core/managers/data-manager.js')

Page({
  onLoad() {
    // 需要调用工厂函数
    this.dashboardService = getDashboardService()
    this.workHistoryService = getWorkHistoryService()
    this.dataManager = getDataManager()
  }
})
```

### 修复后（简洁方案）
```javascript
// 页面顶部无需导入任何 Service/Manager

Page({
  onLoad() {
    // 直接通过 getApp() 获取
    const app = getApp()
    this.dashboardService = app.getDashboardService()
    this.workHistoryService = app.getWorkHistoryService()
    this.dataManager = app.getDataManager()
  }
})
```

## 优势

### 1. 更简洁
- **无需导入**：页面和组件顶部不需要任何 require 语句
- **统一接口**：所有服务都通过 `getApp()` 获取
- **减少代码**：每个文件减少 3-10 行导入代码

### 2. 更可靠
- **避免路径错误**：不需要写相对路径，避免 `../../` 错误
- **统一管理**：所有服务获取都在 `app.js` 中统一管理
- **真正的单例**：确保获取到的是同一个实例

### 3. 更易维护
- **集中管理**：所有服务的获取逻辑都在 `app.js` 中
- **易于重构**：如果需要修改服务获取逻辑，只需修改 `app.js`
- **一致性**：项目中所有地方都使用相同的获取方式

## 使用方式

### 在页面中使用
```javascript
Page({
  onLoad() {
    const app = getApp()
    
    // 获取需要的服务和管理器
    this.dataManager = app.getDataManager()
    this.timeSegmentService = app.getTimeSegmentService()
    this.workHistoryService = app.getWorkHistoryService()
    this.dashboardService = app.getDashboardService()
  },

  someMethod() {
    // 直接使用
    const data = this.timeSegmentService.getDayData(date)
    this.dataManager.saveData(data)
  }
})
```

### 在组件中使用
```javascript
Component({
  attached() {
    const app = getApp()
    
    // 获取需要的服务
    this.baseService = app.getDashboardBaseService()
    this.incomeAdjustmentService = app.getIncomeAdjustmentService()
  },

  methods: {
    someMethod() {
      // 直接使用
      const summary = this.incomeAdjustmentService.getDayAdjustmentSummary(date, workId)
    }
  }
})
```

### 在方法中临时使用
```javascript
someMethod() {
  const app = getApp()
  const dataManager = app.getDataManager()
  const fishingState = dataManager.getCurrentFishingState()
  
  // 使用 fishingState...
}
```

## 可用的方法

### Manager 方法
- `app.getDataManager()`
- `app.getUserManager()`
- `app.getSyncManager()`
- `app.getHolidayManager()`
- `app.getStorageManager()`
- `app.getWorkManager()`
- `app.getSettingsManager()`
- `app.getTimeTrackingManager()`
- `app.getFishingManager()`

### Service 方法
- `app.getIncomeAdjustmentService()`
- `app.getTimeSegmentService()`
- `app.getWorkHistoryService()`
- `app.getStatisticsService()`
- `app.getDataImportExportService()`
- `app.getDashboardService()`
- `app.getDashboardBaseService()`

### 批量获取方法
```javascript
const app = getApp()

// 获取所有服务
const services = app.getServices()
// services.timeSegmentService, services.workHistoryService, etc.

// 获取所有管理器
const managers = app.getManagers()
// managers.dataManager, managers.userManager, etc.
```

## 注意事项

1. **确保 app 已初始化**：在调用 `getApp()` 前确保应用已完成初始化
2. **错误处理**：添加适当的 try-catch 来处理可能的初始化错误
3. **性能考虑**：服务实例会被缓存，避免重复创建

## 备用方案

如果在某些特殊情况下无法使用 `getApp()`（比如在某些工具函数中），可以使用直接导入的方式：

```javascript
const { getTimeSegmentService } = require('../../core/services/time-segment-service.js')
const timeSegmentService = getTimeSegmentService()
```

但这种方式不推荐，应该优先使用 `getApp()` 方式。

## 总结

这个简化方案让代码更加简洁、可靠和易维护。通过统一使用 `getApp().get***()` 的方式，我们避免了复杂的导入逻辑，确保了真正的单例模式，并提高了代码的一致性。
