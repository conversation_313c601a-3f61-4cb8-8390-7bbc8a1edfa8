# 单例模式统一改造总结

## 改造概述

已成功将项目中所有的 Service 和 Manager 文件统一改造为工厂函数模式的单例实现，确保了代码的一致性和可维护性。

## 改造前的状况

### 三种不同的单例实现方式

1. **直接导出实例**：
   ```javascript
   module.exports = new ManagerClass()
   ```
   - fishing-manager.js
   - storage-manager.js
   - work-manager.js
   - settings-manager.js
   - time-tracking-manager.js

2. **静态方法单例**：
   ```javascript
   module.exports = ManagerClass.getInstance()
   ```
   - data-manager.js
   - user-manager.js
   - sync-manager.js

3. **工厂函数模式**：
   ```javascript
   function getManagerName() { ... }
   module.exports = { ManagerClass, getManagerName }
   ```
   - holiday-manager.js（已经是目标格式）

4. **导出类（非单例）**：
   ```javascript
   module.exports = { ServiceClass }
   ```
   - 所有 Service 文件

## 改造后的统一格式

所有文件现在都使用统一的工厂函数模式：

```javascript
// 导出单例实例
let instanceName = null

function getInstanceName() {
  if (!instanceName) {
    instanceName = new ClassName()
  }
  return instanceName
}

module.exports = {
  ClassName,
  getInstanceName
}
```

## 修改的文件列表

### Manager 文件（8个）
1. ✅ storage-manager.js
2. ✅ work-manager.js
3. ✅ settings-manager.js
4. ✅ time-tracking-manager.js
5. ✅ fishing-manager.js
6. ✅ user-manager.js
7. ✅ sync-manager.js
8. ✅ data-manager.js（同时更新了依赖导入）

### Service 文件（7个）
1. ✅ income-adjustment-service.js
2. ✅ time-segment-service.js
3. ✅ work-history-service.js
4. ✅ statistics-service.js
5. ✅ dashboard-service.js
6. ✅ dashboard-base-service.js
7. ✅ data-import-export-service.js

### 应用入口文件
1. ✅ app.js（更新所有导入和方法）

## 新增的统一方法

### app.js 中新增的方法

1. **getServices()**：
   ```javascript
   const services = getApp().getServices()
   // 包含所有服务实例：
   // - incomeAdjustmentService
   // - timeSegmentService
   // - workHistoryService
   // - statisticsService
   // - dataImportExportService
   // - dashboardService
   // - dashboardBaseService
   ```

2. **getManagers()**：
   ```javascript
   const managers = getApp().getManagers()
   // 包含所有管理器实例：
   // - dataManager
   // - userManager
   // - syncManager
   // - holidayManager
   // - storageManager
   // - workManager
   // - settingsManager
   // - timeTrackingManager
   // - fishingManager
   ```

## 使用方式

### 方式1：获取单个服务/管理器（推荐）
```javascript
const app = getApp()
const timeSegmentService = app.getTimeSegmentService()
const dataManager = app.getDataManager()
```

### 方式2：获取所有服务/管理器
```javascript
const app = getApp()
const services = app.getServices()
const managers = app.getManagers()

// 使用
services.timeSegmentService.getDayData(date)
managers.dataManager.getCurrentWork()
```

### 方式3：直接导入（备用方案）
```javascript
const { getTimeSegmentService } = require('../../core/services/time-segment-service.js')
const timeSegmentService = getTimeSegmentService()
```

## 改造的优势

1. **统一性**：所有文件使用相同的单例模式
2. **延迟初始化**：只有在需要时才创建实例
3. **真正的单例**：确保全局只有一个实例
4. **更好的测试支持**：可以重置实例进行单元测试
5. **统一的接口**：通过 app.js 提供统一的访问方式
6. **向后兼容**：既导出类又导出工厂函数

## 注意事项

1. **优先使用 app.js 的方法**：确保获取到正确的单例实例
2. **避免重复实例化**：使用单例模式确保全局只有一个实例
3. **错误处理**：添加适当的错误处理和日志
4. **性能考虑**：服务实例会被缓存，避免重复创建

## 测试建议

建议编写测试来验证：
1. 单例模式是否正确工作
2. 所有的 get 方法是否返回相同的实例
3. getServices() 和 getManagers() 方法是否正常工作
4. 依赖关系是否正确

## 问题修复

### 循环依赖问题修复

在初始改造后发现了循环依赖问题：Service 在构造函数中直接调用 `getDataManager()` 会导致初始化时的循环依赖。

**修复方案：延迟初始化**

将所有 Service 的 dataManager 访问改为延迟初始化模式：

```javascript
class ServiceClass {
  constructor() {
    // 延迟初始化数据管理器，避免循环依赖
    this._dataManager = null
  }

  /**
   * 获取数据管理器实例（延迟初始化）
   * @returns {DataManager} 数据管理器实例
   */
  get dataManager() {
    if (!this._dataManager) {
      const { getDataManager } = require('../managers/data-manager.js')
      this._dataManager = getDataManager()
    }
    return this._dataManager
  }
}
```

**修复的文件：**
- dashboard-service.js
- work-history-service.js
- time-segment-service.js
- statistics-service.js
- income-adjustment-service.js

这次改造确保了项目中所有的 Service 和 Manager 都遵循统一的单例模式，并解决了循环依赖问题，提高了代码的一致性和可维护性。
