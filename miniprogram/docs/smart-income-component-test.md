# 智能填写收入组件测试文档

## 测试目标
验证智能填写收入组件的完整功能，特别是与日收入计算器的集成。

## 测试环境
- 微信开发者工具
- 真机测试（推荐）

## 测试用例

### 1. 基础功能测试

#### 1.1 模态框打开/关闭
- [ ] 点击"智能填写收入"按钮，模态框正常打开
- [ ] 点击关闭按钮，模态框正常关闭
- [ ] 点击遮罩层，模态框正常关闭
- [ ] 模态框动画效果正常

#### 1.2 计算模式切换
- [ ] 默认选中"总收入分配"模式
- [ ] 点击"加班倍率计算"模式，界面正确切换
- [ ] 点击"分类时薪设置"模式，界面正确切换
- [ ] 各模式的输入字段正确显示

### 2. 总收入分配模式测试

#### 2.1 基础输入
- [ ] 输入总收入：1000元
- [ ] 点击"应用计算"，收入按时间比例正确分配
- [ ] 验证各时间段的收入和时薪计算正确

#### 2.2 日收入计算器集成
- [ ] 点击"计算日收入"按钮
- [ ] 日收入计算器正常打开
- [ ] 输入月收入：10000元，工作天数：21.75天
- [ ] 点击"确定使用"
- [ ] **关键测试**: 计算结果（459.77元）正确回填到总收入输入框
- [ ] 再次点击"应用计算"，验证分配结果正确

### 3. 加班倍率模式测试

#### 3.1 基础时薪方式
- [ ] 选择"加班倍率计算"模式
- [ ] 选择"基础时薪"方式
- [ ] 输入基础时薪：50元/小时
- [ ] 输入加班倍率：1.5倍
- [ ] 点击"应用计算"，验证正常工作和加班收入计算正确

#### 3.2 总收入方式
- [ ] 选择"总收入"方式
- [ ] 点击"计算日收入"按钮
- [ ] 在日收入计算器中输入数据并确认
- [ ] **关键测试**: 结果正确回填到加班模式的总收入输入框
- [ ] 输入加班倍率：1.5倍
- [ ] 点击"应用计算"，验证收入分配正确

### 4. 分类时薪模式测试

#### 4.1 分别设置时薪
- [ ] 选择"分类时薪设置"模式
- [ ] 输入工作时薪：40元/小时
- [ ] 输入加班时薪：60元/小时
- [ ] 点击"应用计算"，验证各类型时间段收入计算正确

### 5. 边界情况测试

#### 5.1 输入验证
- [ ] 输入超大数值（如1000000000），显示错误提示
- [ ] 输入负数，系统正确处理
- [ ] 输入非数字字符，系统正确过滤
- [ ] 输入小数位数超过2位，自动截断

#### 5.2 无时间段情况
- [ ] 在没有添加任何时间段的情况下使用智能填写
- [ ] 系统显示合适的提示信息

#### 5.3 只有休息时间段
- [ ] 只添加休息类型的时间段
- [ ] 使用智能填写，验证系统处理正确

### 6. 性能测试

#### 6.1 响应速度
- [ ] 模态框打开速度 < 500ms
- [ ] 计算模式切换响应 < 200ms
- [ ] 收入计算完成时间 < 1s

#### 6.2 内存使用
- [ ] 多次打开关闭模态框，无内存泄漏
- [ ] 组件正确注册和清理全局引用

## 关键验证点

### ✅ 日收入计算器集成
这是本次修复的重点，必须确保：
1. 从智能收入组件能正确打开日收入计算器
2. 日收入计算器的结果能正确回填到智能收入组件
3. 支持两种模式的回填（总收入分配 / 加班倍率）

### ✅ 全局引用机制
验证全局引用机制的可靠性：
1. 组件挂载时正确注册到 app.globalData
2. 组件卸载时正确清理全局引用
3. 回填时能通过全局引用成功获取组件实例

## 测试通过标准
- [ ] 所有基础功能正常
- [ ] 三种计算模式都能正确工作
- [ ] 日收入计算器集成完全正常
- [ ] 边界情况处理合理
- [ ] 无明显性能问题
- [ ] 无控制台错误

## 测试报告模板
```
测试时间：____
测试环境：____
测试结果：通过/失败
失败项目：____
备注：____
```
