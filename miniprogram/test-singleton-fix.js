/**
 * 测试单例模式修复
 * 验证所有 Service 和 Manager 是否正确工作
 */

console.log('=== 开始测试单例模式修复 ===')

try {
  // 测试 Manager 的工厂函数
  console.log('\n--- 测试 Manager 工厂函数 ---')
  
  const { getDataManager } = require('./core/managers/data-manager.js')
  const { getUserManager } = require('./core/managers/user-manager.js')
  const { getSyncManager } = require('./core/managers/sync-manager.js')
  const { getHolidayManager } = require('./core/managers/holiday-manager.js')
  const { getStorageManager } = require('./core/managers/storage-manager.js')
  const { getWorkManager } = require('./core/managers/work-manager.js')
  const { getSettingsManager } = require('./core/managers/settings-manager.js')
  const { getTimeTrackingManager } = require('./core/managers/time-tracking-manager.js')
  const { getFishingManager } = require('./core/managers/fishing-manager.js')

  console.log('✅ 所有 Manager 工厂函数导入成功')

  // 测试 Manager 实例化
  const dataManager1 = getDataManager()
  const dataManager2 = getDataManager()
  console.log('✅ DataManager 单例测试:', dataManager1 === dataManager2 ? '通过' : '失败')

  const userManager1 = getUserManager()
  const userManager2 = getUserManager()
  console.log('✅ UserManager 单例测试:', userManager1 === userManager2 ? '通过' : '失败')

  // 测试 Service 的工厂函数
  console.log('\n--- 测试 Service 工厂函数 ---')
  
  const { getIncomeAdjustmentService } = require('./core/services/income-adjustment-service.js')
  const { getTimeSegmentService } = require('./core/services/time-segment-service.js')
  const { getWorkHistoryService } = require('./core/services/work-history-service.js')
  const { getStatisticsService } = require('./core/services/statistics-service.js')
  const { getDashboardService } = require('./core/services/dashboard-service.js')
  const { getDashboardBaseService } = require('./core/services/dashboard-base-service.js')
  const { getDataImportExportService } = require('./core/services/data-import-export-service.js')

  console.log('✅ 所有 Service 工厂函数导入成功')

  // 测试 Service 实例化
  const timeSegmentService1 = getTimeSegmentService()
  const timeSegmentService2 = getTimeSegmentService()
  console.log('✅ TimeSegmentService 单例测试:', timeSegmentService1 === timeSegmentService2 ? '通过' : '失败')

  const dashboardService1 = getDashboardService()
  const dashboardService2 = getDashboardService()
  console.log('✅ DashboardService 单例测试:', dashboardService1 === dashboardService2 ? '通过' : '失败')

  // 测试 Service 的 dataManager 访问
  console.log('\n--- 测试 Service 的 dataManager 访问 ---')
  
  const timeSegmentService = getTimeSegmentService()
  const workHistoryService = getWorkHistoryService()
  const dashboardService = getDashboardService()

  // 测试延迟初始化的 dataManager
  try {
    const dm1 = timeSegmentService.dataManager
    const dm2 = workHistoryService.dataManager
    const dm3 = dashboardService.dataManager
    
    console.log('✅ TimeSegmentService dataManager 访问成功')
    console.log('✅ WorkHistoryService dataManager 访问成功')
    console.log('✅ DashboardService dataManager 访问成功')
    
    // 验证它们都是同一个实例
    console.log('✅ 所有 Service 的 dataManager 是同一实例:', 
      (dm1 === dm2 && dm2 === dm3) ? '通过' : '失败')
    
  } catch (error) {
    console.error('❌ Service dataManager 访问失败:', error.message)
  }

  console.log('\n=== 单例模式修复测试完成 ===')
  console.log('✅ 所有测试通过！')

} catch (error) {
  console.error('❌ 测试失败:', error)
  console.error('错误堆栈:', error.stack)
}
