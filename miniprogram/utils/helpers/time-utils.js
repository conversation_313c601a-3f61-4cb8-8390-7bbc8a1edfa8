/**
 * 时间工具函数
 * 提供时间格式化、计算等功能
 */

/**
 * 格式化时间为 HH:MM 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
function formatTime(date) {
  if (!date || !(date instanceof Date)) {
    return '00:00'
  }
  
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

/**
 * 格式化时间为 HH:MM:SS 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的时间字符串
 */
function formatTimeHHMMSS(date) {
  if (!date || !(date instanceof Date)) {
    return '00:00:00'
  }
  
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')
  return `${hours}:${minutes}:${seconds}`
}

/**
 * 格式化日期为 YYYY年MM月DD日 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }
  
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  
  return `${year}年${month}月${day}日`
}

/**
 * 格式化日期为 YYYY-MM-DD 格式
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDateToYYYYMMDD(date) {
  if (!date || !(date instanceof Date)) {
    return '未知日期'
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化日期为存储键格式 YYYY-MM-DD
 * @param {Date} date - 日期对象
 * @returns {string} 存储键格式的日期字符串
 */
function formatDateKey(date) {
  if (!date || !(date instanceof Date)) {
    return ''
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}-${month}-${day}`
}

/**
 * 格式化持续时间
 * @param {number} minutes - 分钟数
 * @returns {string} 格式化后的持续时间字符串
 */
function formatDuration(minutes) {
  if (!minutes || minutes <= 0) {
    return '0分钟'
  }
  
  const hours = Math.floor(minutes / 60)
  const mins = Math.round(minutes % 60)
  
  if (hours === 0) {
    return `${mins}分钟`
  } else if (mins === 0) {
    return `${hours}小时`
  } else {
    return `${hours}小时${mins}分钟`
  }
}

/**
 * 计算时间段的收入
 * @param {Object} segment - 时间段对象
 * @returns {number} 收入金额
 */
function calculateIncome(segment) {
  if (!segment || segment.type === 'rest') {
    return 0
  }
  
  return segment.income || 0
}

/**
 * 计算时间段的时薪（动态计算，不依赖存储的hourlyRate字段）
 * @param {Object} segment - 时间段对象
 * @returns {number} 时薪
 */
function calculateHourlyRate(segment) {
  if (!segment || segment.type === 'rest' || !segment.income || segment.income <= 0) {
    return 0
  }

  const durationMinutes = segment.end - segment.start
  const hours = durationMinutes / 60
  return hours > 0 ? segment.income / hours : 0
}

/**
 * 获取日期的开始时间（00:00:00）
 * @param {Date} date - 日期对象
 * @returns {Date} 日期开始时间
 */
function getDateStart(date) {
  const start = new Date(date)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 获取日期的结束时间（23:59:59.999）
 * @param {Date} date - 日期对象
 * @returns {Date} 日期结束时间
 */
function getDateEnd(date) {
  const end = new Date(date)
  end.setHours(23, 59, 59, 999)
  return end
}

/**
 * 获取当周的开始日期（周日）
 * @param {Date} date - 基准日期
 * @returns {Date} 周开始日期
 */
function getWeekStart(date) {
  const start = new Date(date)
  const day = start.getDay()
  start.setDate(start.getDate() - day)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 获取当月的开始日期
 * @param {Date} date - 基准日期
 * @returns {Date} 月开始日期
 */
function getMonthStart(date) {
  const start = new Date(date)
  start.setDate(1)
  start.setHours(0, 0, 0, 0)
  return start
}

/**
 * 检查两个日期是否是同一天
 * @param {Date} date1 - 第一个日期
 * @param {Date} date2 - 第二个日期
 * @returns {boolean} 是否是同一天
 */
function isSameDay(date1, date2) {
  if (!date1 || !date2) {
    return false
  }
  
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate()
}

/**
 * 解析时间字符串为分钟数
 * @param {string} timeStr - 时间字符串 "HH:MM"
 * @returns {number} 分钟数
 */
function parseTimeToMinutes(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0
  }
  
  const [hours, minutes] = timeStr.split(':').map(Number)
  return (hours || 0) * 60 + (minutes || 0)
}

/**
 * 将分钟数转换为时间字符串
 * @param {number} minutes - 分钟数
 * @returns {string} 时间字符串 "HH:MM"
 */
function minutesToTimeString(minutes) {
  const hours = Math.floor(minutes / 60)
  const mins = Math.round(minutes % 60)
  
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * 获取今天的日期
 * @returns {Date} 今天的日期
 */
function getToday() {
  return getDateStart(new Date())
}

/**
 * 格式化文件名用的日期字符串
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串 YYYYMMDD
 */
function formatDateForFilename(date) {
  if (!date || !(date instanceof Date)) {
    return ''
  }
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${year}${month}${day}`
}

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 - 开始日期
 * @param {Date} date2 - 结束日期
 * @returns {number} 天数差
 */
function daysBetween(date1, date2) {
  const oneDay = 24 * 60 * 60 * 1000 // 一天的毫秒数
  const firstDate = new Date(date1)
  const secondDate = new Date(date2)
  
  firstDate.setHours(0, 0, 0, 0)
  secondDate.setHours(0, 0, 0, 0)
  
  return Math.round((secondDate - firstDate) / oneDay)
}

/**
 * 获取指定日期所在月份的所有日期
 * @param {Date} date - 基准日期
 * @returns {Array<Date>} 月份中的所有日期
 */
function getMonthDates(date) {
  const year = date.getFullYear()
  const month = date.getMonth()
  
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  
  const dates = []
  
  for (let day = 1; day <= lastDay.getDate(); day++) {
    dates.push(new Date(year, month, day))
  }
  
  return dates
}

/**
 * 验证时间字符串格式
 * @param {string} timeStr - 时间字符串
 * @returns {boolean} 是否为有效格式
 */
function isValidTimeString(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') {
    return false
  }
  
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/
  return timeRegex.test(timeStr)
}

/**
 * 比较两个时间字符串
 * @param {string} time1 - 第一个时间字符串
 * @param {string} time2 - 第二个时间字符串
 * @returns {number} -1, 0, 1 分别表示 time1 < time2, time1 === time2, time1 > time2
 */
function compareTimeStrings(time1, time2) {
  const minutes1 = parseTimeToMinutes(time1)
  const minutes2 = parseTimeToMinutes(time2)

  if (minutes1 < minutes2) return -1
  if (minutes1 > minutes2) return 1
  return 0
}

// ==================== 新的时间段处理函数 ====================

/**
 * 将时间字符串转换为当日分钟数（支持跨日）
 * @param {string} timeStr - 时间字符串 "HH:MM"
 * @param {boolean} isNextDay - 是否为次日时间
 * @returns {number} 从当日00:00开始的分钟数 (0-2880)
 */
function timeStringToMinutes(timeStr, isNextDay = false) {
  if (!timeStr || typeof timeStr !== 'string') {
    return 0
  }

  const [hours, minutes] = timeStr.split(':').map(Number)
  const totalMinutes = (hours || 0) * 60 + (minutes || 0)

  // 如果是次日时间，加上24小时
  return isNextDay ? totalMinutes + 24 * 60 : totalMinutes
}

/**
 * 将分钟数转换为时间显示字符串（支持跨日显示）
 * @param {number} minutes - 从当日00:00开始的分钟数 (0-2880)
 * @returns {string} 时间显示字符串，如 "22:30" 或 "次日02:30"
 */
function minutesToTimeDisplay(minutes) {
  if (typeof minutes !== 'number' || minutes < 0) {
    return '00:00'
  }

  // 限制在48小时内
  const clampedMinutes = Math.min(minutes, 48 * 60)

  const hours = Math.floor(clampedMinutes / 60)
  const mins = Math.round(clampedMinutes % 60)

  if (hours === 24 && mins === 0) {
    // 正好24:00，显示为24:00而不是次日00:00
    return '24:00'
  } else if (hours > 24) {
    // 超过24小时，显示次日时间
    const nextDayHours = hours - 24
    return `次日${nextDayHours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  } else if (hours === 24) {
    // 24小时但有分钟数，显示次日时间
    return `次日00:${mins.toString().padStart(2, '0')}`
  } else {
    // 当日时间
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
  }
}

/**
 * 将分钟数转换为简单时间字符串（不显示"次日"）
 * @param {number} minutes - 从当日00:00开始的分钟数 (0-2880)
 * @returns {string} 时间字符串 "HH:MM"
 */
function minutesToSimpleTimeString(minutes) {
  if (typeof minutes !== 'number' || minutes < 0) {
    return '00:00'
  }

  const clampedMinutes = Math.min(minutes, 48 * 60)
  const hours = Math.floor(clampedMinutes / 60)
  const mins = Math.round(clampedMinutes % 60)

  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`
}

/**
 * 计算时间段持续时间（分钟）
 * @param {number} startMinutes - 开始时间分钟数
 * @param {number} endMinutes - 结束时间分钟数
 * @returns {number} 持续时间（分钟）
 */
function calculateDurationMinutes(startMinutes, endMinutes) {
  if (typeof startMinutes !== 'number' || typeof endMinutes !== 'number') {
    return 0
  }

  return Math.max(0, endMinutes - startMinutes)
}

/**
 * 格式化毫秒时长为 HH:MM:SS 格式
 * @param {number} milliseconds - 毫秒数
 * @returns {string} 格式化后的时长字符串
 */
function formatMillisecondsDuration(milliseconds) {
  if (!milliseconds || milliseconds <= 0) {
    return '00:00:00'
  }

  const hours = Math.floor(milliseconds / (1000 * 60 * 60))
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000)

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

/**
 * 验证时间段的有效性
 * @param {number} startMinutes - 开始时间分钟数
 * @param {number} endMinutes - 结束时间分钟数
 * @returns {Object} 验证结果 {isValid: boolean, error: string}
 */
function validateTimeSegmentMinutes(startMinutes, endMinutes) {
  if (typeof startMinutes !== 'number' || typeof endMinutes !== 'number') {
    return { isValid: false, error: '时间参数必须是数字' }
  }

  if (startMinutes < 0 || startMinutes > 48 * 60) {
    return { isValid: false, error: '开始时间超出有效范围 (0-2880分钟)' }
  }

  if (endMinutes < 0 || endMinutes > 48 * 60) {
    return { isValid: false, error: '结束时间超出有效范围 (0-2880分钟)' }
  }

  if (endMinutes <= startMinutes) {
    return { isValid: false, error: '结束时间必须晚于开始时间' }
  }

  return { isValid: true, error: '' }
}

/**
 * 格式化时间段范围显示
 * @param {number} startMinutes - 开始时间分钟数
 * @param {number} endMinutes - 结束时间分钟数
 * @returns {string} 时间段范围字符串，如 "09:00-17:30" 或 "22:00-次日02:00"
 */
function formatTimeSegmentRange(startMinutes, endMinutes) {
  const startDisplay = minutesToTimeDisplay(startMinutes)
  const endDisplay = minutesToTimeDisplay(endMinutes)

  return `${startDisplay}-${endDisplay}`
}

/**
 * 检查时间是否为次日时间
 * @param {number} minutes - 分钟数
 * @returns {boolean} 是否为次日时间
 */
function isNextDayTime(minutes) {
  return typeof minutes === 'number' && minutes >= 24 * 60
}

/**
 * 获取相对时间描述
 * @param {Date|string} date - 日期
 * @returns {string} 相对时间描述
 */
function getRelativeTime(date) {
  if (!date) return '从未同步'

  const d = new Date(date)
  if (isNaN(d.getTime())) return '无效时间'

  const now = new Date()
  const diff = now.getTime() - d.getTime()

  if (diff < 0) {
    return '未来时间'
  }

  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    if (days < 7) return `${days}天前`
    if (days < 30) return `${Math.floor(days / 7)}周前`
    if (days < 365) return `${Math.floor(days / 30)}个月前`
    return `${Math.floor(days / 365)}年前`
  }

  if (hours > 0) return `${hours}小时前`
  if (minutes > 0) return `${minutes}分钟前`
  if (seconds > 30) return `${seconds}秒前`

  return '刚刚'
}

// 导出所有函数
module.exports = {
  formatTime,
  formatTimeHHMMSS,
  formatDate,
  formatDateToYYYYMMDD,
  formatDateKey,
  formatDuration,
  calculateIncome,
  calculateHourlyRate,
  getDateStart,
  getDateEnd,
  getWeekStart,
  getMonthStart,
  isSameDay,
  parseTimeToMinutes,
  minutesToTimeString,
  getToday,
  formatDateForFilename,
  daysBetween,
  getMonthDates,
  isValidTimeString,
  compareTimeStrings,
  timeStringToMinutes,
  minutesToTimeDisplay,
  minutesToSimpleTimeString,
  calculateDurationMinutes,
  formatMillisecondsDuration,
  validateTimeSegmentMinutes,
  formatTimeSegmentRange,
  isNextDayTime,
  getRelativeTime
}