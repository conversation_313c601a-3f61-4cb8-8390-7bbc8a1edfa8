/**
 * 导航栏高度计算测试工具
 * 用于验证不同设备上的导航栏高度计算是否正确
 */

/**
 * 模拟不同设备的窗口信息进行测试
 */
function testNavbarHeightCalculation() {
  console.log('=== 导航栏高度计算测试 ===')
  
  // 测试用例：不同设备的窗口信息
  const testCases = [
    {
      name: 'iPhone 6/7/8',
      windowInfo: {
        pixelRatio: 2,
        screenWidth: 375,
        screenHeight: 667,
        statusBarHeight: 20,
        safeArea: { top: 20, left: 0, right: 375, bottom: 667, width: 375, height: 647 }
      }
    },
    {
      name: 'iPhone X/11/12 (有刘海)',
      windowInfo: {
        pixelRatio: 3,
        screenWidth: 375,
        screenHeight: 812,
        statusBarHeight: 44,
        safeArea: { top: 44, left: 0, right: 375, bottom: 778, width: 375, height: 734 }
      }
    },
    {
      name: 'iPhone 6 Plus',
      windowInfo: {
        pixelRatio: 3,
        screenWidth: 414,
        screenHeight: 736,
        statusBarHeight: 20,
        safeArea: { top: 20, left: 0, right: 414, bottom: 736, width: 414, height: 716 }
      }
    },
    {
      name: 'Android 设备 (无safeArea)',
      windowInfo: {
        pixelRatio: 2,
        screenWidth: 360,
        screenHeight: 640,
        statusBarHeight: 24,
        safeArea: null
      }
    }
  ]
  
  testCases.forEach(testCase => {
    console.log(`\n--- ${testCase.name} ---`)
    const result = calculateNavbarHeight(testCase.windowInfo)
    console.log('计算结果:', result)
  })
}

/**
 * 计算导航栏高度的核心函数
 * @param {Object} windowInfo - 窗口信息
 * @returns {Object} 计算结果
 */
function calculateNavbarHeight(windowInfo) {
  const { safeArea, statusBarHeight, screenWidth } = windowInfo
  
  // 1. 获取更准确的状态栏高度（优先使用safeArea.top）
  const actualStatusBarHeight = (safeArea && safeArea.top) || statusBarHeight || 20
  
  // 2. 计算rpx到px的精确转换比例
  const rpxToPxRatio = screenWidth / 750
  
  // 3. 计算导航栏内容高度（88rpx转为px）
  const navbarContentHeight = Math.round(88 * rpxToPxRatio)
  
  // 4. 计算总的导航栏高度
  const totalNavbarHeight = actualStatusBarHeight + navbarContentHeight
  
  return {
    actualStatusBarHeight,
    navbarContentHeight,
    totalNavbarHeight,
    screenWidth,
    rpxToPxRatio: rpxToPxRatio.toFixed(4),
    calculation: `${actualStatusBarHeight}px (状态栏) + ${navbarContentHeight}px (导航栏内容) = ${totalNavbarHeight}px`
  }
}

/**
 * 在小程序中运行测试
 */
function runTestInMiniProgram() {
  try {
    // 使用新的工具函数
    const { calculateNavbarHeight } = require('./navbar-height')
    const result = calculateNavbarHeight({ enableLog: true, logPrefix: 'Test' })

    console.log('当前设备导航栏高度计算:', result)

    return result
  } catch (error) {
    console.error('测试失败:', error)
    return null
  }
}

module.exports = {
  testNavbarHeightCalculation,
  calculateNavbarHeight,
  runTestInMiniProgram
}
