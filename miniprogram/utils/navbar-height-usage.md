# 导航栏高度工具使用指南

## 简介

`navbar-height.js` 提供了统一的导航栏高度计算功能，确保在不同设备上的一致性。

## 基本使用

### 1. 在页面中使用

```javascript
// pages/example/index.js
Page({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0
  },

  onLoad: function() {
    // 使用工具函数设置导航栏高度
    const { setNavbarHeightData } = require('../../utils/navbar-height')
    setNavbarHeightData(this, {
      enableLog: true,
      logPrefix: 'ExamplePage'
    })
  }
})
```

### 2. 在组件中使用

```javascript
// components/example/index.js
Component({
  data: {
    statusBarHeight: 0,
    navbarHeight: 0
  },

  lifetimes: {
    attached: function() {
      // 使用工具函数设置导航栏高度
      const { setNavbarHeightData } = require('../../utils/navbar-height')
      setNavbarHeightData(this, {
        enableLog: true,
        logPrefix: 'ExampleComponent'
      })
    }
  }
})
```

### 3. 在WXML中使用

```html
<!-- 使用动态样式 -->
<view class="main-content" style="margin-top: {{navbarHeight}}px; height: calc(100vh - {{navbarHeight}}px);">
  <!-- 内容 -->
</view>

<!-- 或者使用padding-top -->
<view class="container" style="padding-top: {{navbarHeight}}px;">
  <!-- 内容 -->
</view>
```

## 高级使用

### 1. 只获取计算结果

```javascript
const { calculateNavbarHeight } = require('../../utils/navbar-height')

const result = calculateNavbarHeight({
  enableLog: false
})

console.log('导航栏高度:', result.navbarHeight)
console.log('状态栏高度:', result.statusBarHeight)
```

### 2. 获取样式字符串

```javascript
const { getNavbarStyle } = require('../../utils/navbar-height')

// 获取margin-top样式
const marginStyle = getNavbarStyle({ type: 'marginTop' })
// 结果: "margin-top: 64px;"

// 获取padding-top样式（带额外高度）
const paddingStyle = getNavbarStyle({ 
  type: 'paddingTop', 
  extraHeight: 20 
})
// 结果: "padding-top: 84px;"

// 获取高度样式
const heightStyle = getNavbarStyle({ type: 'height' })
// 结果: "height: calc(100vh - 64px);"
```

### 3. 在WXML中使用样式函数

```javascript
// 在页面/组件的JS中
Page({
  data: {
    containerStyle: '',
    contentStyle: ''
  },

  onLoad: function() {
    const { getNavbarStyle } = require('../../utils/navbar-height')
    
    this.setData({
      containerStyle: getNavbarStyle({ type: 'paddingTop' }),
      contentStyle: getNavbarStyle({ type: 'marginTopAndHeight' })
    })
  }
})
```

```html
<!-- 在WXML中使用 -->
<view class="container" style="{{containerStyle}}">
  <view class="content" style="{{contentStyle}}">
    <!-- 内容 -->
  </view>
</view>
```

## API 参考

### calculateNavbarHeight(options)

计算导航栏高度的核心函数。

**参数：**
- `options.enableLog` (boolean) - 是否启用日志输出，默认false
- `options.logPrefix` (string) - 日志前缀，默认'NavbarHeight'

**返回值：**
```javascript
{
  statusBarHeight: number,    // 状态栏高度
  navbarHeight: number,       // 总导航栏高度
  navbarContentHeight: number, // 导航栏内容高度
  rpxToPxRatio: number,       // rpx到px转换比例
  screenWidth: number         // 屏幕宽度
}
```

### setNavbarHeightData(context, options)

为页面或组件设置导航栏高度数据。

**参数：**
- `context` - 页面或组件的this上下文
- `options` - 同calculateNavbarHeight的options

### getNavbarStyle(options)

获取导航栏相关的样式字符串。

**参数：**
- `options.type` (string) - 样式类型
  - `'marginTop'` - margin-top样式
  - `'paddingTop'` - padding-top样式
  - `'height'` - height样式
  - `'minHeight'` - min-height样式
  - `'marginTopAndHeight'` - margin-top和height组合
- `options.extraHeight` (number) - 额外高度，单位px

## 最佳实践

### 1. 统一使用工具函数

```javascript
// ✅ 推荐
const { setNavbarHeightData } = require('../../utils/navbar-height')
setNavbarHeightData(this, { enableLog: true, logPrefix: 'MyPage' })

// ❌ 不推荐 - 重复代码
const windowInfo = wx.getWindowInfo()
// ... 重复的计算逻辑
```

### 2. 合理使用日志

```javascript
// 开发环境启用日志
setNavbarHeightData(this, {
  enableLog: true,  // 开发时启用
  logPrefix: 'MyComponent'
})

// 生产环境可以关闭日志
setNavbarHeightData(this, {
  enableLog: false, // 生产时关闭
  logPrefix: 'MyComponent'
})
```

### 3. 选择合适的样式类型

```javascript
// 对于主内容区域，使用marginTopAndHeight
const contentStyle = getNavbarStyle({ type: 'marginTopAndHeight' })

// 对于容器，使用paddingTop
const containerStyle = getNavbarStyle({ type: 'paddingTop' })

// 对于需要额外间距的情况
const overlayStyle = getNavbarStyle({ 
  type: 'paddingTop', 
  extraHeight: 40 
})
```

## 注意事项

1. **确保在正确的生命周期调用**：页面使用`onLoad`，组件使用`attached`
2. **data中需要预定义字段**：确保data中有`statusBarHeight`和`navbarHeight`字段
3. **路径正确**：注意require的相对路径是否正确
4. **兼容性**：工具函数已处理各种设备兼容性问题，无需额外处理
