// 测试设置工作计划组件的增强功能
console.log('=== 设置工作计划组件增强功能测试 ===')

// 测试默认时间段创建
function testDefaultTimeInputsCreation() {
  console.log('\n1. 测试默认时间段创建...')
  
  // 模拟组件的 _createDefaultTimeInputsWithIncome 方法
  function createDefaultTimeInputsWithIncome(targetDailyIncome) {
    const defaultSegments = [
      {
        startTime: '09:00',
        endTime: '12:00',
        type: 'work',
        typeIndex: 0
      },
      {
        startTime: '12:00',
        endTime: '14:00',
        type: 'rest',
        typeIndex: 1
      },
      {
        startTime: '14:00',
        endTime: '18:00',
        type: 'work',
        typeIndex: 0
      }
    ]

    // 计算总工作时长（分钟）
    let totalWorkMinutes = 0
    defaultSegments.forEach(segment => {
      if (segment.type === 'work') {
        const [startHour, startMinute] = segment.startTime.split(':').map(Number)
        const [endHour, endMinute] = segment.endTime.split(':').map(Number)
        const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)
        totalWorkMinutes += duration
      }
    })

    // 计算平均时薪
    const averageHourlyRate = totalWorkMinutes > 0 ? (targetDailyIncome / (totalWorkMinutes / 60)) : 60

    // 创建时间段对象并分配收入
    return defaultSegments.map(segment => {
      const timeInput = {
        startTime: segment.startTime,
        endTime: segment.endTime,
        type: segment.type,
        typeIndex: segment.typeIndex,
        isStartNextDay: false,
        isEndNextDay: false,
        _lastUpdatedBy: 'income'
      }

      if (segment.type === 'work') {
        const [startHour, startMinute] = segment.startTime.split(':').map(Number)
        const [endHour, endMinute] = segment.endTime.split(':').map(Number)
        const duration = (endHour * 60 + endMinute) - (startHour * 60 + startMinute)
        const segmentIncome = Math.round((duration / totalWorkMinutes) * targetDailyIncome * 100) / 100

        timeInput.income = segmentIncome
        timeInput.incomeText = segmentIncome.toString()
        timeInput.hourlyRate = Math.round(averageHourlyRate * 100) / 100
        timeInput.hourlyRateText = timeInput.hourlyRate.toFixed(2)
      } else {
        timeInput.income = 0
        timeInput.incomeText = ''
        timeInput.hourlyRate = 0
        timeInput.hourlyRateText = '0.00'
      }

      return timeInput
    })
  }

  const targetIncome = 500
  const defaultInputs = createDefaultTimeInputsWithIncome(targetIncome)
  
  console.log('✓ 默认时间段数量:', defaultInputs.length)
  console.log('✓ 工作时间段数量:', defaultInputs.filter(input => input.type === 'work').length)
  console.log('✓ 休息时间段数量:', defaultInputs.filter(input => input.type === 'rest').length)
  
  const totalIncome = defaultInputs.reduce((sum, input) => sum + input.income, 0)
  console.log('✓ 总收入分配:', totalIncome, '目标收入:', targetIncome)
  console.log('✓ 收入分配准确:', Math.abs(totalIncome - targetIncome) < 0.01)
  
  return defaultInputs
}

// 测试智能添加时间段
function testSmartAddTimeInput() {
  console.log('\n2. 测试智能添加时间段...')
  
  // 模拟现有时间段
  const existingInputs = [
    {
      startTime: '09:00',
      endTime: '12:00',
      type: 'work',
      typeIndex: 0,
      hourlyRate: 60,
      isEndNextDay: false
    }
  ]
  
  // 模拟添加时间段的逻辑
  function addTimeInput(timeInputs) {
    const lastInput = timeInputs[timeInputs.length - 1]
    
    function addHours(timeStr, hours) {
      const [hour, minute] = timeStr.split(':').map(Number)
      const newHour = hour + hours
      return `${String(newHour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
    }
    
    let newInput = {
      startTime: lastInput ? lastInput.endTime : '09:00',
      endTime: addHours(lastInput ? lastInput.endTime : '09:00', 1),
      type: 'work',
      typeIndex: 0,
      income: 0,
      incomeText: '',
      hourlyRate: 0,
      hourlyRateText: '',
      isStartNextDay: false,
      isEndNextDay: false
    }

    if (lastInput) {
      // 智能类型选择
      if (lastInput.type === 'work' || lastInput.type === 'overtime') {
        newInput.type = 'rest'
        newInput.typeIndex = 1
      } else if (lastInput.type === 'rest') {
        const endHour = parseInt(lastInput.endTime.split(':')[0])
        if (endHour >= 18) {
          newInput.type = 'overtime'
          newInput.typeIndex = 2
        }
      }

      // 继承跨日状态
      newInput.isStartNextDay = lastInput.isEndNextDay
    }

    return [...timeInputs, newInput]
  }
  
  // 测试添加第一个时间段（应该是休息）
  const afterFirst = addTimeInput(existingInputs)
  console.log('✓ 第一次添加后类型:', afterFirst[1].type, '(应该是 rest)')
  
  // 测试添加第二个时间段（应该是工作）
  const afterSecond = addTimeInput(afterFirst)
  console.log('✓ 第二次添加后类型:', afterSecond[2].type, '(应该是 work)')
  
  // 测试晚上时间添加（应该是加班）
  const lateInput = [{
    startTime: '18:00',
    endTime: '19:00',
    type: 'rest',
    typeIndex: 1,
    isEndNextDay: false
  }]
  const afterLate = addTimeInput(lateInput)
  console.log('✓ 晚上添加后类型:', afterLate[1].type, '(应该是 overtime)')
  
  return true
}

// 测试合理时薪计算
function testReasonableHourlyRateCalculation() {
  console.log('\n3. 测试合理时薪计算...')
  
  // 模拟计算合理时薪的方法
  function calculateReasonableHourlyRate(existingTimeInputs, dailyIncome = 500) {
    const workSegments = existingTimeInputs.filter(input =>
      (input.type === 'work' || input.type === 'overtime') && input.hourlyRate > 0
    )

    if (workSegments.length > 0) {
      const totalHourlyRate = workSegments.reduce((sum, input) => sum + input.hourlyRate, 0)
      return Math.round((totalHourlyRate / workSegments.length) * 100) / 100
    }

    const estimatedWorkHours = 8
    const estimatedHourlyRate = dailyIncome / estimatedWorkHours
    return Math.round(estimatedHourlyRate * 100) / 100
  }
  
  // 测试有现有时薪的情况
  const withExistingRates = [
    { type: 'work', hourlyRate: 60 },
    { type: 'work', hourlyRate: 80 }
  ]
  const avgRate = calculateReasonableHourlyRate(withExistingRates)
  console.log('✓ 现有时薪平均值:', avgRate, '(应该是 70)')
  
  // 测试没有现有时薪的情况
  const withoutRates = [
    { type: 'work', hourlyRate: 0 }
  ]
  const defaultRate = calculateReasonableHourlyRate(withoutRates, 400)
  console.log('✓ 默认时薪计算:', defaultRate, '(应该是 50)')
  
  return avgRate === 70 && defaultRate === 50
}

// 测试时间段排序
function testTimeInputSorting() {
  console.log('\n4. 测试时间段排序...')
  
  // 模拟时间转换和排序
  function timeToMinutes(timeStr, isNextDay = false) {
    const [hour, minute] = timeStr.split(':').map(Number)
    const totalMinutes = hour * 60 + minute
    return isNextDay ? totalMinutes + 24 * 60 : totalMinutes
  }
  
  function sortTimeInputsByStartTime(timeInputs) {
    return timeInputs.slice().sort((a, b) => {
      const aMinutes = timeToMinutes(a.startTime, a.isStartNextDay)
      const bMinutes = timeToMinutes(b.startTime, b.isStartNextDay)
      return aMinutes - bMinutes
    })
  }
  
  // 测试乱序时间段
  const unsortedInputs = [
    { startTime: '14:00', isStartNextDay: false },
    { startTime: '09:00', isStartNextDay: false },
    { startTime: '02:00', isStartNextDay: true },
    { startTime: '18:00', isStartNextDay: false }
  ]
  
  const sorted = sortTimeInputsByStartTime(unsortedInputs)
  const sortedTimes = sorted.map(input => 
    `${input.startTime}${input.isStartNextDay ? '(次日)' : ''}`
  )
  
  console.log('✓ 排序前:', unsortedInputs.map(i => i.startTime))
  console.log('✓ 排序后:', sortedTimes)
  console.log('✓ 排序正确:', sortedTimes[0] === '09:00' && sortedTimes[3] === '02:00(次日)')
  
  return true
}

// 运行所有测试
function runAllTests() {
  try {
    const test1 = testDefaultTimeInputsCreation()
    const test2 = testSmartAddTimeInput()
    const test3 = testReasonableHourlyRateCalculation()
    const test4 = testTimeInputSorting()
    
    if (test2 && test3 && test4) {
      console.log('\n=== 所有测试通过 ===')
      console.log('✓ 默认时间段创建功能正常')
      console.log('✓ 智能添加时间段功能正常')
      console.log('✓ 合理时薪计算功能正常')
      console.log('✓ 时间段排序功能正常')
      
      console.log('\n增强功能总结：')
      console.log('1. 初始化时自动创建合理的默认时间段（工作-休息-工作）')
      console.log('2. 智能添加时间段，自动选择合适的类型')
      console.log('3. 自动计算合理的时薪和收入分配')
      console.log('4. 自动排序时间段，保持时间顺序')
      console.log('5. 支持跨日时间段的处理')
      
    } else {
      console.log('\n=== 部分测试失败 ===')
      console.log('需要进一步检查和修复')
    }
    
  } catch (error) {
    console.error('测试执行失败:', error)
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests }
} else {
  // 在浏览器或小程序环境中直接运行
  runAllTests()
}
